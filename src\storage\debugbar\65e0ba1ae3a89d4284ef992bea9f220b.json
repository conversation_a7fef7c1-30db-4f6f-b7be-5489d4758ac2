{"__meta": {"id": "65e0ba1ae3a89d4284ef992bea9f220b", "datetime": "2025-07-23 08:08:13", "utime": 1753279693.782276, "method": "POST", "uri": "/servicio/455752/subrogacion/save", "ip": "**********"}, "php": {"version": "7.2.34", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753279677.75492, "end": 1753279693.7823, "duration": 16.027379989624023, "duration_str": "16.03s", "measures": [{"label": "Booting", "start": 1753279677.75492, "relative_start": 0, "end": 1753279683.71897, "relative_end": 1753279683.71897, "duration": 5.964050054550171, "duration_str": "5.96s", "params": [], "collector": null}, {"label": "Application", "start": 1753279682.956307, "relative_start": 5.20138692855835, "end": 1753279693.782302, "relative_end": 1.9073486328125e-06, "duration": 10.825994968414307, "duration_str": "10.83s", "params": [], "collector": null}]}, "memory": {"peak_usage": 16777216, "peak_usage_str": "16MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST servicio/{id}/subrogacion/save", "middleware": "web, App\\Http\\Middleware\\CheckClientAccess, renew.password", "domain": "{cpath}.renapp.com", "controller": "App\\Http\\Controllers\\Services\\SubrogacionSortController@save", "namespace": "App\\Http\\Controllers", "prefix": null, "where": [], "file": "app/Http/Controllers/Services/SubrogacionSortController.php:49-147"}, "queries": {"nb_statements": 26, "nb_failed_statements": 0, "accumulated_duration": 5.48066, "accumulated_duration_str": "5.48s", "statements": [{"sql": "select * from `users` where `id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 46, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 49, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 70, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14004, "duration_str": "140.04ms", "stmt_id": "/app/Http/Middleware/RequestLoggerMiddleware.php:28", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 22}, {"index": 48, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 51, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 72, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13788, "duration_str": "137.88ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:22", "connection": "ebdb"}, {"sql": "select * from `user_clients` where `client_id` = '3' and `user_id` = '18' limit 1", "type": "query", "params": [], "bindings": ["3", "18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 25}, {"index": 47, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 50, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 71, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14188, "duration_str": "141.88ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:25", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '455752' and `service_id` = '101' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["455752", "101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 72}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13728, "duration_str": "137.28ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:72", "connection": "ebdb"}, {"sql": "select * from `subrogacion_sort` where `subrogacion_sort`.`activity_id` in ('455752')", "type": "query", "params": [], "bindings": ["455752"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 19, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 72}, {"index": 27, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 30, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 88, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13671, "duration_str": "136.71ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:72", "connection": "ebdb"}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 83}, {"index": 16, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 19, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 53, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 77, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:83", "connection": "ebdb"}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1681}, {"index": 9, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 10, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 18, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 21, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 79, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/ActionController.php:1681", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '455752' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["455752"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1684}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13705, "duration_str": "137.05ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1684", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` = '101' limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1690}, {"index": 19, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 20, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 28, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 31, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 89, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14629, "duration_str": "146.29ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1690", "connection": "ebdb"}, {"sql": "select * from `service_states` where `service_id` = '101' and `state_id` = '249' limit 1", "type": "query", "params": [], "bindings": ["101", "249"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1693}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13921, "duration_str": "139.21ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1693", "connection": "ebdb"}, {"sql": "select * from `state_actions` where `state_id` = '249' and `action_id` = '466' limit 1", "type": "query", "params": [], "bindings": ["249", "466"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1702}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.19219999999999998, "duration_str": "192.2ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1702", "connection": "ebdb"}, {"sql": "select * from `action_service_states` where `service_id` = '101' and `action_id` = '466' limit 1", "type": "query", "params": [], "bindings": ["101", "466"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1711}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14276, "duration_str": "142.76ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1711", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` = '250' limit 1", "type": "query", "params": [], "bindings": ["250"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1714}, {"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 19, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 27, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 30, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 88, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13723, "duration_str": "137.23ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1714", "connection": "ebdb"}, {"sql": "insert into `activity_actions` (`activity_id`, `action_id`, `old_state_id`, `new_state_id`, `description`, `old_user_id`, `new_user_id`, `author_id`, `updated_at`, `created_at`) values ('455752', '466', '249', '250', 'Actualización de subrogación', '18', '18', '18', '2025-07-23 08:08:07', '2025-07-23 08:08:07')", "type": "query", "params": [], "bindings": ["455752", "466", "249", "250", "Actualizaci&oacute;n de subrogaci&oacute;n", "18", "18", "18", "2025-07-23 08:08:07", "2025-07-23 08:08:07"], "hints": [], "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1730}, {"index": 16, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16138, "duration_str": "161.38ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1730", "connection": "ebdb"}, {"sql": "insert into \"audits\" (\"old_values\", \"new_values\", \"event\", \"auditable_id\", \"auditable_type\", \"user_id\", \"user_type\", \"url\", \"ip_address\", \"user_agent\", \"tags\", \"updated_at\", \"created_at\") values ('[]', '{\"activity_id\":455752,\"action_id\":466,\"old_state_id\":249,\"new_state_id\":250,\"description\":\"Actualizaci\\u00f3n de subrogaci\\u00f3n\",\"old_user_id\":18,\"new_user_id\":18,\"author_id\":18,\"id\":1018533}', 'created', '1018533', 'App\\ActivityAction', '18', 'App\\User', 'https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?', '**********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '2025-07-23 08:08:07', '2025-07-23 08:08:07') returning \"id\"", "type": "query", "params": [], "bindings": ["[]", "{&quot;activity_id&quot;:455752,&quot;action_id&quot;:466,&quot;old_state_id&quot;:249,&quot;new_state_id&quot;:250,&quot;description&quot;:&quot;Actualizaci\\u00f3n de subrogaci\\u00f3n&quot;,&quot;old_user_id&quot;:18,&quot;new_user_id&quot;:18,&quot;author_id&quot;:18,&quot;id&quot;:1018533}", "created", "1018533", "App\\ActivityAction", "18", "App\\User", "https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?", "**********", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "", "2025-07-23 08:08:07", "2025-07-23 08:08:07"], "hints": [], "backtrace": [{"index": 22, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/Auditor.php", "line": 69}, {"index": 24, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "line": 38}, {"index": 32, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1730}, {"index": 33, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 34, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 42, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 45, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 88, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 103, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 1.6043699999999999, "duration_str": "1.6s", "stmt_id": "/vendor/owen-it/laravel-auditing/src/Auditor.php:69", "connection": "mnk-audit-15886.qa"}, {"sql": "update `activities` set `state_id` = '250', `updated_at` = '2025-07-23 08:08:09' where `id` = '455752'", "type": "query", "params": [], "bindings": ["250", "2025-07-23 08:08:09", "455752"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1734}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.17924, "duration_str": "179.24ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1734", "connection": "ebdb"}, {"sql": "insert into \"audits\" (\"old_values\", \"new_values\", \"event\", \"auditable_id\", \"auditable_type\", \"user_id\", \"user_type\", \"url\", \"ip_address\", \"user_agent\", \"tags\", \"updated_at\", \"created_at\") values ('{\"state_id\":249}', '{\"state_id\":250}', 'updated', '455752', 'App\\Activity', '18', 'App\\User', 'https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?', '**********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '2025-07-23 08:08:09', '2025-07-23 08:08:09') returning \"id\"", "type": "query", "params": [], "bindings": ["{&quot;state_id&quot;:249}", "{&quot;state_id&quot;:250}", "updated", "455752", "App\\Activity", "18", "App\\User", "https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?", "**********", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "", "2025-07-23 08:08:09", "2025-07-23 08:08:09"], "hints": [], "backtrace": [{"index": 22, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/Auditor.php", "line": 69}, {"index": 24, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "line": 52}, {"index": 32, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1734}, {"index": 33, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 34, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 42, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 45, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 88, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 103, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.22516999999999998, "duration_str": "225.17ms", "stmt_id": "/vendor/owen-it/laravel-auditing/src/Auditor.php:69", "connection": "mnk-audit-15886.qa"}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1736}, {"index": 9, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 196}, {"index": 10, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 18, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 21, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 79, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/ActionController.php:1736", "connection": "ebdb"}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1681}, {"index": 9, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 10, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 18, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 21, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 79, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/ActionController.php:1681", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '455752' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["455752"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1684}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13969, "duration_str": "139.69ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1684", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` = '101' limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1690}, {"index": 19, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 20, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 28, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 31, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 89, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13663, "duration_str": "136.63ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1690", "connection": "ebdb"}, {"sql": "select * from `service_states` where `service_id` = '101' and `state_id` = '250' limit 1", "type": "query", "params": [], "bindings": ["101", "250"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1693}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13604, "duration_str": "136.04ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1693", "connection": "ebdb"}, {"sql": "select * from `state_actions` where `state_id` = '250' and `action_id` = '470' limit 1", "type": "query", "params": [], "bindings": ["250", "470"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1702}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14039, "duration_str": "140.39ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1702", "connection": "ebdb"}, {"sql": "select * from `action_service_states` where `service_id` = '101' and `action_id` = '470' limit 1", "type": "query", "params": [], "bindings": ["101", "470"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1711}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13832, "duration_str": "138.32ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1711", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` = '251' limit 1", "type": "query", "params": [], "bindings": ["251"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1714}, {"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 19, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 27, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 30, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 88, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13775, "duration_str": "137.75ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1714", "connection": "ebdb"}, {"sql": "insert into `activity_actions` (`activity_id`, `action_id`, `old_state_id`, `new_state_id`, `description`, `old_user_id`, `new_user_id`, `author_id`, `updated_at`, `created_at`) values ('455752', '470', '250', '251', 'Actualización de subrogación', '18', '18', '18', '2025-07-23 08:08:10', '2025-07-23 08:08:10')", "type": "query", "params": [], "bindings": ["455752", "470", "250", "251", "Actualizaci&oacute;n de subrogaci&oacute;n", "18", "18", "18", "2025-07-23 08:08:10", "2025-07-23 08:08:10"], "hints": [], "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1730}, {"index": 16, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14837999999999998, "duration_str": "148.38ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1730", "connection": "ebdb"}, {"sql": "insert into \"audits\" (\"old_values\", \"new_values\", \"event\", \"auditable_id\", \"auditable_type\", \"user_id\", \"user_type\", \"url\", \"ip_address\", \"user_agent\", \"tags\", \"updated_at\", \"created_at\") values ('[]', '{\"activity_id\":455752,\"action_id\":470,\"old_state_id\":250,\"new_state_id\":251,\"description\":\"Actualizaci\\u00f3n de subrogaci\\u00f3n\",\"old_user_id\":18,\"new_user_id\":18,\"author_id\":18,\"id\":1018534}', 'created', '1018534', 'App\\ActivityAction', '18', 'App\\User', 'https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?', '**********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '2025-07-23 08:08:10', '2025-07-23 08:08:10') returning \"id\"", "type": "query", "params": [], "bindings": ["[]", "{&quot;activity_id&quot;:455752,&quot;action_id&quot;:470,&quot;old_state_id&quot;:250,&quot;new_state_id&quot;:251,&quot;description&quot;:&quot;Actualizaci\\u00f3n de subrogaci\\u00f3n&quot;,&quot;old_user_id&quot;:18,&quot;new_user_id&quot;:18,&quot;author_id&quot;:18,&quot;id&quot;:1018534}", "created", "1018534", "App\\ActivityAction", "18", "App\\User", "https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?", "**********", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "", "2025-07-23 08:08:10", "2025-07-23 08:08:10"], "hints": [], "backtrace": [{"index": 22, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/Auditor.php", "line": 69}, {"index": 24, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "line": 38}, {"index": 32, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1730}, {"index": 33, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 34, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 42, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 45, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 88, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 103, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.22294999999999998, "duration_str": "222.95ms", "stmt_id": "/vendor/owen-it/laravel-auditing/src/Auditor.php:69", "connection": "mnk-audit-15886.qa"}, {"sql": "update `activities` set `state_id` = '251', `updated_at` = '2025-07-23 08:08:10' where `id` = '455752'", "type": "query", "params": [], "bindings": ["251", "2025-07-23 08:08:10", "455752"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1734}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16751, "duration_str": "167.51ms", "stmt_id": "/app/Http/Controllers/ActionController.php:1734", "connection": "ebdb"}, {"sql": "insert into \"audits\" (\"old_values\", \"new_values\", \"event\", \"auditable_id\", \"auditable_type\", \"user_id\", \"user_type\", \"url\", \"ip_address\", \"user_agent\", \"tags\", \"updated_at\", \"created_at\") values ('{\"state_id\":250}', '{\"state_id\":251}', 'updated', '455752', 'App\\Activity', '18', 'App\\User', 'https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?', '**********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '2025-07-23 08:08:11', '2025-07-23 08:08:11') returning \"id\"", "type": "query", "params": [], "bindings": ["{&quot;state_id&quot;:250}", "{&quot;state_id&quot;:251}", "updated", "455752", "App\\Activity", "18", "App\\User", "https://oceanica-qa.renapp.com/servicio/455752/subrogacion/save?", "**********", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "", "2025-07-23 08:08:11", "2025-07-23 08:08:11"], "hints": [], "backtrace": [{"index": 22, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/Auditor.php", "line": 69}, {"index": 24, "namespace": null, "name": "/vendor/owen-it/laravel-auditing/src/AuditableObserver.php", "line": 52}, {"index": 32, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1734}, {"index": 33, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 34, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 42, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 45, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 88, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 103, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.21736000000000003, "duration_str": "217.36ms", "stmt_id": "/vendor/owen-it/laravel-auditing/src/Auditor.php:69", "connection": "mnk-audit-15886.qa"}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/ActionController.php", "line": 1736}, {"index": 9, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 203}, {"index": 10, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 90}, {"index": 18, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 21, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 79, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/ActionController.php:1736", "connection": "ebdb"}, {"sql": "insert into `activity_action_documents` (`activity_action_id`, `name`, `path`, `updated_at`, `created_at`) values ('1018534', 'referencia-2', 'activity_action_document/subrogacion_455752_0_20250723_080811.pdf', '2025-07-23 08:08:13', '2025-07-23 08:08:13')", "type": "query", "params": [], "bindings": ["1018534", "referencia-2", "activity_action_document/subrogacion_455752_0_20250723_080811.pdf", "2025-07-23 08:08:13", "2025-07-23 08:08:13"], "hints": [], "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 124}, {"index": 23, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 26, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 84, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13695, "duration_str": "136.95ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:124", "connection": "ebdb"}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "backtrace": [{"index": 8, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 140}, {"index": 16, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 19, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 53, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 77, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0, "duration_str": "", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:140", "connection": "ebdb"}]}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"wilmer<PERSON>za10\"\n  \"user\" => array:50 [\n    \"id\" => 18\n    \"user_type\" => \"\"\n    \"email\" => \"<EMAIL>\"\n    \"affiliate_id\" => null\n    \"created_at\" => \"2024-09-17 10:51:15\"\n    \"updated_at\" => \"2025-07-21 15:22:52\"\n    \"full_name\" => \"WILMERR MAZA BANDA\"\n    \"first_name\" => \"WILMERR\"\n    \"last_name\" => \"MAZA BANDA\"\n    \"username\" => \"wilmermaza10\"\n    \"photo\" => \"user_photo/at8RH2yiiVxOjsYpnJozCU3S7UrqTy5O4T49x4i8.jpeg\"\n    \"area_id\" => 1\n    \"position_id\" => null\n    \"deleted_at\" => null\n    \"ascribed\" => 0\n    \"old_password1\" => \"$2y$10$cFzxSS3xlT4izkkAK7kze.tBRQ7Jm9N6uh1D4r8gCrLH39uAyhljq\"\n    \"old_password2\" => \"$2y$10$sLHd.4zpr6JC9myNAorkHOxRsO3Oys2y7SG0mbTvN/d5Bo3uYhUoS\"\n    \"old_password3\" => \"$2y$10$PSY9nL.qDVMLjriYKtLHoeh9rX1awTWv.y5aEsdmoUpfe.1rqGhnC\"\n    \"last_login\" => \"2025-07-21 15:22:52\"\n    \"next_update\" => \"2025-08-01 10:24:44\"\n    \"extra_data\" => \"\"\n    \"zoom_api_key\" => null\n    \"zoom_api_secret\" => null\n    \"identification_number\" => \"**********\"\n    \"creator\" => null\n    \"has_schedule\" => \"0\"\n    \"company_id\" => null\n    \"department\" => null\n    \"municipality\" => null\n    \"ocupation\" => null\n    \"phone\" => \"**********\"\n    \"doc_type\" => \"CF\"\n    \"medical_record_number\" => null\n    \"license_number\" => null\n    \"rethus\" => null\n    \"signature\" => null\n    \"name_area\" => null\n    \"code_mnk\" => \"000010\"\n    \"brokerage_name\" => \"BCR CORREDORA DE SEGUROS SA\"\n    \"new_business_email\" => \"\"\n    \"advisor_name\" => \"000010\"\n    \"type_inter\" => \"G\"\n    \"correduria\" => null\n    \"code_correduria\" => \"009101\"\n    \"tipo_corredor\" => \"C\"\n    \"provider_id\" => null\n    \"resp_acsel_login\" => null\n    \"tomador_id\" => 0\n    \"unique_code\" => \"651827\"\n    \"autorizados\" => \"12486,42222\"\n  ]\n]", "api": "array:2 [\n  \"name\" => \"Guest\"\n  \"user\" => array:1 [\n    \"guest\" => true\n  ]\n]"}, "names": "web: wilmermaza10"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc", "_previous": "array:1 [\n  \"url\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "18", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"format": "html", "content_type": "application/json", "status_text": "Internal Server Error", "status_code": "500", "request_query": "[]", "request_request": "array:4 [\n  \"_token\" => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  \"monto_recuperado\" => \"₡50.000,00\"\n  \"monto_no_recuperado\" => \"₡600.000,00\"\n  \"estado_proceso\" => \"EN_CURSO\"\n]", "request_headers": "array:19 [\n  \"cookie\" => array:1 [\n    0 => \"XSRF-TOKEN=eyJpdiI6ImY4M1FGMGNjNm80aEgxY3dxdVZiK1E9PSIsInZhbHVlIjoiTnZsdnRQeGx2RWx6SDV4anBlM3VrdkRaOUVvK3NuZVBidlwveWhFaVpIYWtUWlQxNVA0STJqUXV5aEI4eVdQNTQiLCJtYWMiOiIzNjI3ZTQ5MjBjYTlmYzRlYTA1MjAwOTcwNDgwZGZiZjkzMGQ5YzZlZjVjYjkwNmUyYzZmMzRmY2NlYjFmN2MxIn0%3D; laravel_session_cookie=eyJpdiI6IlBSSjkrajR6aXk5aHpxRDlxRGlIZ0E9PSIsInZhbHVlIjoiZ3ltRjlcL2s1bjRVN2VkUndQd3dhRW54M0E0dDQrZXhVXC9Pc0ZBNldqYmRlbm0yTVQwNGphUmYrXC91QzdCTEdoTHNIak5oQ1FRUjdZaUhXSEV5UlRNaUNJak9zMEdvWVF6ZUNGamg3UWVuZ24zNnVXVVwvdXNKZXFnY0pRdm5OMzBjIiwibWFjIjoiZGRiZDZhMGUzNmI0OTMyOTgyODdmZjk3NDM3M2I4OTM2OTc4ODVmMTdlOGZhMmRlMjgwMGNmYzUzNTY2MWVhZSJ9\"\n  ]\n  \"accept-language\" => array:1 [\n    0 => \"en,es-ES;q=0.9,es;q=0.8\"\n  ]\n  \"accept-encoding\" => array:1 [\n    0 => \"gzip, deflate, br, zstd\"\n  ]\n  \"referer\" => array:1 [\n    0 => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  ]\n  \"sec-fetch-dest\" => array:1 [\n    0 => \"empty\"\n  ]\n  \"sec-fetch-mode\" => array:1 [\n    0 => \"cors\"\n  ]\n  \"sec-fetch-site\" => array:1 [\n    0 => \"same-origin\"\n  ]\n  \"origin\" => array:1 [\n    0 => \"https://oceanica-qa.renapp.com\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"multipart/form-data; boundary=----WebKitFormBoundarywnELQB9KCINrdpEa\"\n  ]\n  \"accept\" => array:1 [\n    0 => \"*/*\"\n  ]\n  \"user-agent\" => array:1 [\n    0 => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  ]\n  \"x-requested-with\" => array:1 [\n    0 => \"XMLHttpRequest\"\n  ]\n  \"sec-ch-ua-mobile\" => array:1 [\n    0 => \"?0\"\n  ]\n  \"sec-ch-ua\" => array:1 [\n    0 => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  ]\n  \"x-csrf-token\" => array:1 [\n    0 => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  ]\n  \"sec-ch-ua-platform\" => array:1 [\n    0 => \"\"Windows\"\"\n  ]\n  \"content-length\" => array:1 [\n    0 => \"57425\"\n  ]\n  \"connection\" => array:1 [\n    0 => \"keep-alive\"\n  ]\n  \"host\" => array:1 [\n    0 => \"oceanica-qa.renapp.com\"\n  ]\n]", "request_server": "array:65 [\n  \"PHP_EXTRA_CONFIGURE_ARGS\" => \"--enable-fpm --with-fpm-user=www-data --with-fpm-group=www-data --disable-cgi\"\n  \"LANGUAGE\" => \"es_ES.UTF-8\"\n  \"HOSTNAME\" => \"b44486792d1f\"\n  \"PHP_INI_DIR\" => \"/usr/local/etc/php\"\n  \"HOME\" => \"/var/www\"\n  \"PHP_LDFLAGS\" => \"-Wl,-O1 -pie\"\n  \"PHP_CFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_VERSION\" => \"7.2.34\"\n  \"GPG_KEYS\" => \"1729F83938DA44E27BA0F4D3DBDB397470D12172 B1B44D8F021E4E2D6021E995DC9FF8D3EE5AF27F\"\n  \"PHP_CPPFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_ASC_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz.asc\"\n  \"PHP_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz\"\n  \"PATH\" => \"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"\n  \"LANG\" => \"es_ES.UTF-8\"\n  \"PHPIZE_DEPS\" => \"autoconf \\t\\tdpkg-dev \\t\\tfile \\t\\tg++ \\t\\tgcc \\t\\tlibc-dev \\t\\tmake \\t\\tpkg-config \\t\\tre2c\"\n  \"LC_ALL\" => \"es_ES.UTF-8\"\n  \"PWD\" => \"/var/www/html\"\n  \"PHP_SHA256\" => \"409e11bc6a2c18707dfc44bc61c820ddfd81e17481470f3405ee7822d8379903\"\n  \"USER\" => \"www-data\"\n  \"HTTP_COOKIE\" => \"XSRF-TOKEN=eyJpdiI6ImY4M1FGMGNjNm80aEgxY3dxdVZiK1E9PSIsInZhbHVlIjoiTnZsdnRQeGx2RWx6SDV4anBlM3VrdkRaOUVvK3NuZVBidlwveWhFaVpIYWtUWlQxNVA0STJqUXV5aEI4eVdQNTQiLCJtYWMiOiIzNjI3ZTQ5MjBjYTlmYzRlYTA1MjAwOTcwNDgwZGZiZjkzMGQ5YzZlZjVjYjkwNmUyYzZmMzRmY2NlYjFmN2MxIn0%3D; laravel_session_cookie=eyJpdiI6IlBSSjkrajR6aXk5aHpxRDlxRGlIZ0E9PSIsInZhbHVlIjoiZ3ltRjlcL2s1bjRVN2VkUndQd3dhRW54M0E0dDQrZXhVXC9Pc0ZBNldqYmRlbm0yTVQwNGphUmYrXC91QzdCTEdoTHNIak5oQ1FRUjdZaUhXSEV5UlRNaUNJak9zMEdvWVF6ZUNGamg3UWVuZ24zNnVXVVwvdXNKZXFnY0pRdm5OMzBjIiwibWFjIjoiZGRiZDZhMGUzNmI0OTMyOTgyODdmZjk3NDM3M2I4OTM2OTc4ODVmMTdlOGZhMmRlMjgwMGNmYzUzNTY2MWVhZSJ9\"\n  \"HTTP_ACCEPT_LANGUAGE\" => \"en,es-ES;q=0.9,es;q=0.8\"\n  \"HTTP_ACCEPT_ENCODING\" => \"gzip, deflate, br, zstd\"\n  \"HTTP_REFERER\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  \"HTTP_SEC_FETCH_DEST\" => \"empty\"\n  \"HTTP_SEC_FETCH_MODE\" => \"cors\"\n  \"HTTP_SEC_FETCH_SITE\" => \"same-origin\"\n  \"HTTP_ORIGIN\" => \"https://oceanica-qa.renapp.com\"\n  \"HTTP_CONTENT_TYPE\" => \"multipart/form-data; boundary=----WebKitFormBoundarywnELQB9KCINrdpEa\"\n  \"HTTP_ACCEPT\" => \"*/*\"\n  \"HTTP_USER_AGENT\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  \"HTTP_X_REQUESTED_WITH\" => \"XMLHttpRequest\"\n  \"HTTP_SEC_CH_UA_MOBILE\" => \"?0\"\n  \"HTTP_SEC_CH_UA\" => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  \"HTTP_X_CSRF_TOKEN\" => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  \"HTTP_SEC_CH_UA_PLATFORM\" => \"\"Windows\"\"\n  \"HTTP_CONTENT_LENGTH\" => \"57425\"\n  \"HTTP_CONNECTION\" => \"keep-alive\"\n  \"HTTP_HOST\" => \"oceanica-qa.renapp.com\"\n  \"PATH_INFO\" => \"\"\n  \"SCRIPT_FILENAME\" => \"/var/www/html/public/index.php\"\n  \"REDIRECT_STATUS\" => \"200\"\n  \"SERVER_NAME\" => \"oceanica.renapp.us\"\n  \"SERVER_PORT\" => \"443\"\n  \"SERVER_ADDR\" => \"**********\"\n  \"REMOTE_PORT\" => \"59986\"\n  \"REMOTE_ADDR\" => \"**********\"\n  \"SERVER_SOFTWARE\" => \"nginx/1.26.3\"\n  \"GATEWAY_INTERFACE\" => \"CGI/1.1\"\n  \"HTTPS\" => \"on\"\n  \"REQUEST_SCHEME\" => \"https\"\n  \"SERVER_PROTOCOL\" => \"HTTP/1.1\"\n  \"DOCUMENT_ROOT\" => \"/var/www/html/public\"\n  \"DOCUMENT_URI\" => \"/index.php\"\n  \"REQUEST_URI\" => \"/servicio/455752/subrogacion/save\"\n  \"SCRIPT_NAME\" => \"/index.php\"\n  \"CONTENT_LENGTH\" => \"57425\"\n  \"CONTENT_TYPE\" => \"multipart/form-data; boundary=----WebKitFormBoundarywnELQB9KCINrdpEa\"\n  \"REQUEST_METHOD\" => \"POST\"\n  \"QUERY_STRING\" => \"\"\n  \"FCGI_ROLE\" => \"RESPONDER\"\n  \"PHP_SELF\" => \"/index.php\"\n  \"REQUEST_TIME_FLOAT\" => 1753279677.7549\n  \"REQUEST_TIME\" => 1753279677\n  \"argv\" => []\n  \"argc\" => 0\n]", "request_cookies": "array:2 [\n  \"XSRF-TOKEN\" => null\n  \"laravel_session_cookie\" => \"BJvB71yW4W7wGKpeFrH8iWn6wGY1p14rZv9U2owd\"\n]", "response_headers": "array:5 [\n  \"cache-control\" => array:1 [\n    0 => \"no-cache, private\"\n  ]\n  \"date\" => array:1 [\n    0 => \"Wed, 23 Jul 2025 14:08:13 GMT\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"application/json\"\n  ]\n  \"set-cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6InZWQUxERnp0dEM1S3pGU3BSYXFHSnc9PSIsInZhbHVlIjoiY0tOR2dPUmw4eDJUc3liOVNIWmk5SmdPRG8zXC9DYUYxQk1va1FqQkM2cDNcL3lUUHQwVWVjSnI4Um9GdWpHeDY5IiwibWFjIjoiNzQ4M2MzOGJlMzlkMmZhMzI1ODI2NDlhOTMzNTE2NTM4ZmIyOGYzMTMzNTYxNjNmNGFkMGE1YTJhNjNkNjY0MiJ9; expires=Thu, 24-Jul-2025 10:08:13 GMT; Max-Age=72000; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6IlBHeHk5M29KeDVWN3hkWVwvdUlqb3FRPT0iLCJ2YWx1ZSI6ImFPTWdlNEtEZUNPMm9weG1ZRk5NSTBSa0g5dWlKcFJnRjdjOExKQ2N1WjNmTU0xcGZOMVRVQXF6VkNOMVhIQzA3ZHdrcXJmbUVNd2pJVElkT1JGYVwvS2Z6OURJQmx4dG9qOW1CSjBqY0drZVQwU3FPMXdqazZrQXBVZ0k1eml0SCIsIm1hYyI6Ijc0OGNkZTRkYzJhN2Y1NzRkM2ZiNjc4OTMyY2U2OWQ1OWExODkwZjQ0YTE3YWU2NWFhZjZmY2Q3NTlmZmMxMzEifQ%3D%3D; expires=Thu, 24-Jul-2025 10:08:13 GMT; Max-Age=72000; path=/; httponly\"\n  ]\n  \"Set-Cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6InZWQUxERnp0dEM1S3pGU3BSYXFHSnc9PSIsInZhbHVlIjoiY0tOR2dPUmw4eDJUc3liOVNIWmk5SmdPRG8zXC9DYUYxQk1va1FqQkM2cDNcL3lUUHQwVWVjSnI4Um9GdWpHeDY5IiwibWFjIjoiNzQ4M2MzOGJlMzlkMmZhMzI1ODI2NDlhOTMzNTE2NTM4ZmIyOGYzMTMzNTYxNjNmNGFkMGE1YTJhNjNkNjY0MiJ9; expires=Thu, 24-Jul-2025 10:08:13 GMT; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6IlBHeHk5M29KeDVWN3hkWVwvdUlqb3FRPT0iLCJ2YWx1ZSI6ImFPTWdlNEtEZUNPMm9weG1ZRk5NSTBSa0g5dWlKcFJnRjdjOExKQ2N1WjNmTU0xcGZOMVRVQXF6VkNOMVhIQzA3ZHdrcXJmbUVNd2pJVElkT1JGYVwvS2Z6OURJQmx4dG9qOW1CSjBqY0drZVQwU3FPMXdqazZrQXBVZ0k1eml0SCIsIm1hYyI6Ijc0OGNkZTRkYzJhN2Y1NzRkM2ZiNjc4OTMyY2U2OWQ1OWExODkwZjQ0YTE3YWU2NWFhZjZmY2Q3NTlmZmMxMzEifQ%3D%3D; expires=Thu, 24-Jul-2025 10:08:13 GMT; path=/; httponly\"\n  ]\n]", "path_info": "/servicio/455752/subrogacion/save", "session_attributes": "array:5 [\n  \"_token\" => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  \"_previous\" => array:1 [\n    \"url\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  ]\n  \"_flash\" => array:2 [\n    \"old\" => []\n    \"new\" => []\n  ]\n  \"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d\" => 18\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n]"}}