<div class="title">
    <i class="dropdown icon"></i>
    Caso judicial por subrogación
</div>
<div class="content">
    <div class="four fields">
        <div class="field ">
            <label for="expediente_judicial">Número de expediente judicial asociado</label>
            <div class="ui input">
                <input id="expediente_judicial" type="text" autocomplete="off" readonly
                    value="<?php echo e($subrogacionSort->expediente_judicial ?? ''); ?>"
                    placeholder="Ingrese número de expediente" />
            </div>
        </div>

        <div class="field">
            <label for="juzgado_caso">Juzgado donde se tramita el caso</label>
            <div class="ui input">
                <input id="juzgado_caso" type="text" autocomplete="off" readonly
                    value="<?php echo e($subrogacionSort->juzgado_caso ?? ''); ?>" placeholder="Ingrese juzgado" />
            </div>
        </div>

        <div class="field">
            <label for="numero_proceso">Número de proceso judicial</label>
            <div class="ui input">
                <input id="numero_proceso" type="text" autocomplete="off" readonly
                    value="<?php echo e($subrogacionSort->numero_proceso ?? ''); ?>" placeholder="Ingrese número de proceso" />
            </div>
        </div>

        <div class="field">
            <label for="fecha_inicio_juicio">Fecha de inicio del juicio</label>
            <div class="ui input">
                <input value="<?php echo e($subrogacionSort->fecha_inicio_juicio ?? ''); ?>" id="fecha_inicio_juicio" type="date"
                    readonly autocomplete="off" />
            </div>
        </div>
    </div>

    <div class="four fields">
        <?php
            // Clave guardada en BD (puede ser null)
            $actual = $subrogacionSort->estado_proceso ?? null;

            // Array de CLAVES en el orden definido
            $claves = array_keys($ESTADOS_PROCESO);         // ['PENDIENTE', 'EN_CURSO', …]

            // Posición de la clave actual
            $idxActual = array_search($actual, $claves, true);

            // Clave siguiente (o null si no hay)
            $claveSiguiente = $idxActual !== false && $idxActual + 1 < count($claves)
                ? $claves[$idxActual + 1]
                : null;
        ?>

        <div class="field">
            <label for="estado_proceso">Estado del proceso judicial</label>

            <div id="estado_proceso_dropdown"
                class="ui fluid search selection dropdown <?php if($actual == 'ARCHIVO_DEFINITIVO'): ?> grayed-input <?php endif; ?>">
                <input type="hidden" name="estado_proceso" id="estado_proceso" value="<?php echo e($actual); ?>">

                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>

                <div class="menu">
                    <?php $__currentLoopData = $ESTADOS_PROCESO; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clave => $texto): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Habilitamos solo la clave actual y la siguiente
                            $disabled = !($clave === $actual || $clave === $claveSiguiente);
                        ?>

                        <div class="item <?php echo e($disabled ? 'disabled' : ''); ?>" data-value="<?php echo e($clave); ?>">
                            <?php echo e($texto); ?>

                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <div class="field" id="grp-soportes">
            <label>Soportes</label>
            <input type="file" name="soportes[]" multiple accept="application/pdf,image/*">
        </div>
    </div>
</div>

<script>
    $(function () {

        const estadosConSoporte = new Set([
            'EN_CURSO',
            'SENTENCIA_FIRME',
            'COBRO_EJECUCION',
            'ARCHIVO_DEFINITIVO'
        ]);

        const $dropdown = $('#estado_proceso_dropdown'); // div .dropdown
        const $hidden = $('#estado_proceso');          // input hidden
        const $grpSup = $('#grp-soportes');            // div field soportes

        /* helper: mostrar / ocultar */
        function toggleSoportes(valor) {
            $grpSup.toggle(estadosConSoporte.has(valor));
        }

        /* — inicializar Semantic UI y hookup onChange — */
        $dropdown.dropdown({
            onChange: function (value /*data-value*/, _text, _choice) {
                $hidden.val(value);          // sincroniza hidden
                toggleSoportes(value);       // muestra / oculta
            }
        });

        /* — estado al cargar la página — */
        toggleSoportes($hidden.val());
    });
</script>