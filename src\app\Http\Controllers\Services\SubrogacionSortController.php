<?php

namespace App\Http\Controllers\Services;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Service;
use App\Activity;
use App\Http\Controllers\ActionController;
use App\Actions\ActionSubrogacionSort;
use App\ActivityActionDocument;
use Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use PDF;

class SubrogacionSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $request, $cpath, $id)
    {

        $activitySubrogacion = Activity::with(['subrogacion_sort', 'affiliate', 'parent_activity.gis_sort', 'parent_activity.parent_activity.policy_sort'])->where('id', $id)
            ->where('service_id', Service::SERVICE_SUBROGACION_MNK)
            ->first();

        $activityGis = $activitySubrogacion->parent_activity;
        $activityPolicy = $activityGis->parent_activity;



        return view('services.subrogacion.form.form', [
            'activity' => $activitySubrogacion,
            'subrogacionSort' => $activitySubrogacion->subrogacion_sort,
            'activityGis' => $activityGis,
            'activityPolicy' => $activityPolicy
        ]);
    }

    public function save(Request $request, $cpath, $id)
    {
        /* 1️⃣  Validación básica */
        $request->validate(
            [
                'estado_proceso' => ['required', 'string'],
                'soportes' => ['required', 'array', 'min:1'],
                'soportes.*' => ['file', 'mimes:pdf,jpg,jpeg,png', 'max:10240'],
            ],
            [
                'soportes.required' => 'Debe adjuntar al menos un archivo de soporte.',
                'soportes.array' => 'El campo soportes debe ser una lista de archivos.',
                'soportes.min' => 'Debe subir al menos un archivo.',
                'soportes.*.mimes' => 'Solo se permiten archivos PDF o imágenes (JPG/PNG).',
                'soportes.*.max' => 'Cada archivo no puede exceder 10 MB.',
            ]
        );


        /* 2️⃣  Recuperar actividad + subrogación */
        $activity = Activity::with('subrogacion_sort')
            ->where('id', $id)
            ->where('service_id', Service::SERVICE_SUBROGACION_MNK)
            ->firstOrFail();

        $subrogacionSort = $activity->subrogacion_sort;

        if ($subrogacionSort->estado_proceso === $request->estado_proceso) {
            return response()->json([
                'success' => false,
                'message' => 'El estado del proceso no puede ser el mismo.'
            ], 422);
        }

        DB::beginTransaction();

        try {
            /* 3️⃣  Cambiar estado y crear acciones */
            $activityAction = $this->procesarEstadoProceso(
                $subrogacionSort,
                $activity,
                $request->estado_proceso
            );

            $montoRecuperadoInput = $request->input('monto_recuperado', '0');

            // Remove '$', '₡' (pesos/colones signs) and thousands separators ('.')
            $cleanedMonto = str_replace(['$', '₡', '.'], '', $montoRecuperadoInput);

            // Replace the comma decimal separator with a dot decimal separator
            $finalMonto = str_replace(',', '.', $cleanedMonto);

            // Convert the string to a float
            $subrogacionSort->monto_recuperado = (float) $finalMonto;

            /* 4️⃣  Guardar cada soporte */

            $files = $request->file('soportes');          // siempre array (por validación)

            foreach ($files as $idx => $file) {
                if (!$file->isValid())
                    continue;          // salta corruptos
                $original = $file->getClientOriginalName();      // Ej. 'Factura Julio.pdf'

                // 2️⃣  Slug + prefijo timestamp + id
                $slug = Str::slug(pathinfo($original, PATHINFO_FILENAME)); // 'factura-julio'
                $ext = $file->getClientOriginalExtension() ?: 'bin';
                $timeStamp = now()->format('Ymd_His');
                $filename = "subrogacion_{$activity->id}_{$idx}_{$timeStamp}.{$ext}";
                $path = "activity_action_document/{$filename}";

                // S3
                Storage::disk('s3')->putFileAs(
                    'activity_action_document',
                    $file,
                    $filename
                );

                // Registro BD

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = $slug;
                $activityActionDocument->path = $path;
                $activityActionDocument->save();

            }

            /* 5️⃣  Persiste cambios de subrogación */
            $subrogacionSort->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Datos y soportes guardados correctamente.'
            ]);


        } catch (\Throwable $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error inesperado. Intenta de nuevo.'
            ], 500);
        }
    }


    /**
     * Procesa el cambio de estado y crea las acciones correspondientes.
     */
    private function procesarEstadoProceso(
        $subrogacionSort,
        $activity,
        $estado
    ) {
        try {
            /* Mapa estado  ➜  [ estadoBD , acciónPrincipal , acciónSoporte ] */
            $MAP = [
                'EN_CURSO' => [
                    'state' => 'EN_CURSO',
                    'main' => ActionSubrogacionSort::ACTUALIZACION_DE_SUBROGACION,
                    'file' => ActionSubrogacionSort::CARGAR_CONSTANCIA_DE_PROCESO_JUDICIAL_EN_CURSO,
                ],
                'SENTENCIA_FIRME' => [
                    'state' => 'SENTENCIA_FIRME',
                    'main' => ActionSubrogacionSort::ACTUALIZACION_DEL_CASO,
                    'file' => ActionSubrogacionSort::CARGAR_SOPORTE_DE_SENTENCIA_EN_FIRME,
                ],
                'COBRO_EJECUCION' => [
                    'state' => 'COBRO_EJECUCION',
                    'main' => ActionSubrogacionSort::ACTUALIZACION_DE_LA_SENTENCIA_EN_FIRME,
                    'file' => ActionSubrogacionSort::CARGAR_SOPORTE_DE_COBRO_EN_EJECUCION,
                ],
                'ARCHIVO_DEFINITIVO' => [
                    'state' => 'ARCHIVO_DEFINITIVO',
                    'main' => ActionSubrogacionSort::ACTUALIZACION_DEL_COBRO_EN_EJECUCION,
                    'file' => ActionSubrogacionSort::CARGAR_SOPORTE_DE_ARCHIVO_DEFINITIVO,
                ],
            ];

            if (!isset($MAP[$estado])) {
                throw new \InvalidArgumentException('Estado de proceso no válido.');
            }

            $cfg = $MAP[$estado];

            // 1️⃣  Actualiza el estado
            $subrogacionSort->estado_proceso = $cfg['state'];

            // 2️⃣  Acción principal
            ActionController::create(
                $activity->id,
                $cfg['main'],
                'Actualización de subrogación'
            );

            // 3️⃣  Acción de soporte (devuelta por si la necesitas)
            return ActionController::create(
                $activity->id,
                $cfg['file'],
                'Actualización de subrogación'
            );

        } catch (\Throwable $th) {
            throw $th;
        }


    }


    public function downloadDocument(Request $request, $cpath, $id)
    {
        $activity = Activity::with(['subrogacion_sort', 'affiliate', 'parent_activity.gis_sort', 'parent_activity.parent_activity.affiliate', 'parent_activity.parent_activity.policy_sort'])->where('id', $id)
            ->where('service_id', Service::SERVICE_SUBROGACION_MNK)
            ->first();

        $subrogacion = $activity->subrogacion_sort;

        $activityGis = $activity->parent_activity;
        $activityPolicy = $activityGis->parent_activity;

        $tipo = strtoupper($request->input('tipo', ''));

        $VIEWS = [
            'ESTADO_CUENTA' => 'estado_cuenta_individual',
            'CERTIF_GASTOS' => 'certificacion_gastos',
        ];

        if (!isset($VIEWS[$tipo])) {
            return response()->json([
                'message' => 'Tipo de documento no válido.'
            ], 422);
        }

        /* 3️⃣  Datos para la vista PDF */
        $documentData = [
            'activity' => $activity,
            'subrogacion' => $subrogacion,
            'activityGis' => $activityGis,
            'activityPolicy' => $activityPolicy
        ];

        /* 4️⃣  Renderizar */
        $viewName = "services.subrogacion.documents." . $VIEWS[$tipo];

        $pdf = PDF::loadView($viewName, $documentData);
        /* 5️⃣  Nombre de descarga */
        $fileName = "{$VIEWS[$tipo]}_{$activity->id}.pdf";

        return $pdf->download($fileName);
    }
}
