{"__meta": {"id": "5d0f9a2c28a8a42b066b6d80304a88a0", "datetime": "2025-07-23 07:31:17", "utime": 1753277477.764503, "method": "GET", "uri": "/afiliado/1228962", "ip": "**********"}, "php": {"version": "7.2.34", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753277446.686736, "end": 1753277477.764549, "duration": 31.077812910079956, "duration_str": "31.08s", "measures": [{"label": "Booting", "start": 1753277446.686736, "relative_start": 0, "end": 1753277453.273263, "relative_end": 1753277453.273263, "duration": 6.586526870727539, "duration_str": "6.59s", "params": [], "collector": null}, {"label": "Application", "start": 1753277452.500981, "relative_start": 5.814244985580444, "end": 1753277477.764554, "relative_end": 5.0067901611328125e-06, "duration": 25.263572931289673, "duration_str": "25.26s", "params": [], "collector": null}]}, "memory": {"peak_usage": 2097152, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "affiliate (resources/views/affiliate.blade.php)", "param_count": 35, "params": ["client", "affiliate", "determination_its", "determination_pcls", "invalidity_states", "meetings", "notifies", "dictum_receptions", "pqrs", "tutelages", "quotations", "policy_sort", "variations_sort", "policy_sort_collection", "constancy_sort", "affiliate_workforce_report", "affiliate_taker_report", "medical_services_sort", "pe_it_sort", "pe_ip_sort", "pe_mpt_sort", "medication", "gist_sort", "medical_bills", "pe_recognition_expenses", "supplier_mot", "affiliate_payment", "liquidation", "renewal", "reintegrate", "medical_services_secondary_care_sort", "medical_services_sort_select", "administrative_payment", "subrogacion_sort", "documentation_tracking"], "type": "blade"}, {"name": "affiliate.affiliate_profile (resources/views/affiliate/affiliate_profile.blade.php)", "param_count": 208, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "client", "affiliate", "determination_its", "determination_pcls", "invalidity_states", "meetings", "notifies", "dictum_receptions", "pqrs", "tutelages", "quotations", "policy_sort", "variations_sort", "policy_sort_collection", "constancy_sort", "affiliate_workforce_report", "affiliate_taker_report", "medical_services_sort", "pe_it_sort", "pe_ip_sort", "pe_mpt_sort", "medication", "gist_sort", "medical_bills", "pe_recognition_expenses", "supplier_mot", "affiliate_payment", "liquidation", "renewal", "reintegrate", "medical_services_secondary_care_sort", "medical_services_sort_select", "administrative_payment", "subrogacion_sort", "documentation_tracking"], "type": "blade"}, {"name": "affiliate.affiliate_historical (resources/views/affiliate/affiliate_historical.blade.php)", "param_count": 217, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "client", "affiliate", "determination_its", "determination_pcls", "invalidity_states", "meetings", "notifies", "dictum_receptions", "pqrs", "tutelages", "quotations", "policy_sort", "variations_sort", "policy_sort_collection", "constancy_sort", "affiliate_workforce_report", "affiliate_taker_report", "medical_services_sort", "pe_it_sort", "pe_ip_sort", "pe_mpt_sort", "medication", "gist_sort", "medical_bills", "pe_recognition_expenses", "supplier_mot", "affiliate_payment", "liquidation", "renewal", "reintegrate", "medical_services_secondary_care_sort", "medical_services_sort_select", "administrative_payment", "subrogacion_sort", "documentation_tracking", "__currentLoopData", "loop", "activity", "dataPolicy", "dataGis", "typeService", "serviceType", "v", "k"], "type": "blade"}, {"name": "layouts.main (resources/views/layouts/main.blade.php)", "param_count": 208, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "client", "affiliate", "determination_its", "determination_pcls", "invalidity_states", "meetings", "notifies", "dictum_receptions", "pqrs", "tutelages", "quotations", "policy_sort", "variations_sort", "policy_sort_collection", "constancy_sort", "affiliate_workforce_report", "affiliate_taker_report", "medical_services_sort", "pe_it_sort", "pe_ip_sort", "pe_mpt_sort", "medication", "gist_sort", "medical_bills", "pe_recognition_expenses", "supplier_mot", "affiliate_payment", "liquidation", "renewal", "reintegrate", "medical_services_secondary_care_sort", "medical_services_sort_select", "administrative_payment", "subrogacion_sort", "documentation_tracking"], "type": "blade"}]}, "route": {"uri": "GET afiliado/{id}", "middleware": "web, App\\Http\\Middleware\\CheckClientAccess, renew.password", "domain": "{cpath}.renapp.com", "controller": "App\\Http\\Controllers\\HomeController@viewAffiliate", "namespace": "App\\Http\\Controllers", "prefix": null, "where": [], "file": "app/Http/Controllers/HomeController.php:36-302"}, "queries": {"nb_statements": 149, "nb_failed_statements": 0, "accumulated_duration": 21.154310000000002, "accumulated_duration_str": "21.15s", "statements": [{"sql": "select * from `users` where `id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 46, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 49, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 70, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14168, "duration_str": "141.68ms", "stmt_id": "/app/Http/Middleware/RequestLoggerMiddleware.php:28", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 22}, {"index": 48, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 51, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 72, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1372, "duration_str": "137.2ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:22", "connection": "ebdb"}, {"sql": "select * from `user_clients` where `client_id` = '3' and `user_id` = '18' limit 1", "type": "query", "params": [], "bindings": ["3", "18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 25}, {"index": 47, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 50, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 71, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14006, "duration_str": "140.06ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:25", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 38}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13724, "duration_str": "137.24ms", "stmt_id": "/app/Http/Controllers/HomeController.php:38", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `client_id` = '3' and `id` = '1228962' and `affiliates`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3", "1228962"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 42}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14775, "duration_str": "147.75ms", "stmt_id": "/app/Http/Controllers/HomeController.php:42", "connection": "ebdb"}, {"sql": "select * from `employments` where `employments`.`affiliate_id` in ('1228962') and `employments`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1228962"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 19, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 42}, {"index": 27, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 30, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 64, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 88, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14983000000000002, "duration_str": "149.83ms", "stmt_id": "/app/Http/Controllers/HomeController.php:42", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '74' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "74"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 48}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14052, "duration_str": "140.52ms", "stmt_id": "/app/Http/Controllers/HomeController.php:48", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '75' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "75"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 54}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14368999999999998, "duration_str": "143.69ms", "stmt_id": "/app/Http/Controllers/HomeController.php:54", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '77' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "77"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 60}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13952, "duration_str": "139.52ms", "stmt_id": "/app/Http/Controllers/HomeController.php:60", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '101' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 66}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14145, "duration_str": "141.45ms", "stmt_id": "/app/Http/Controllers/HomeController.php:66", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('101')", "type": "query", "params": [], "bindings": ["101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 66}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13791, "duration_str": "137.91ms", "stmt_id": "/app/Http/Controllers/HomeController.php:66", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('1', '249', '251')", "type": "query", "params": [], "bindings": ["1", "249", "251"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 66}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13585, "duration_str": "135.85ms", "stmt_id": "/app/Http/Controllers/HomeController.php:66", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('18')", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 66}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13851, "duration_str": "138.51ms", "stmt_id": "/app/Http/Controllers/HomeController.php:66", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '80' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "80"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 72}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13769, "duration_str": "137.69ms", "stmt_id": "/app/Http/Controllers/HomeController.php:72", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '79' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "79"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 78}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13759, "duration_str": "137.59ms", "stmt_id": "/app/Http/Controllers/HomeController.php:78", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '83' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "83"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 84}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14007, "duration_str": "140.07ms", "stmt_id": "/app/Http/Controllers/HomeController.php:84", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('83')", "type": "query", "params": [], "bindings": ["83"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 84}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13801, "duration_str": "138.01ms", "stmt_id": "/app/Http/Controllers/HomeController.php:84", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('63')", "type": "query", "params": [], "bindings": ["63"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 84}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13847, "duration_str": "138.47ms", "stmt_id": "/app/Http/Controllers/HomeController.php:84", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('4094')", "type": "query", "params": [], "bindings": ["4094"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 84}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14083, "duration_str": "140.83ms", "stmt_id": "/app/Http/Controllers/HomeController.php:84", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '88' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "88"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 89}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13946, "duration_str": "139.46ms", "stmt_id": "/app/Http/Controllers/HomeController.php:89", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('88')", "type": "query", "params": [], "bindings": ["88"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 89}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14031, "duration_str": "140.31ms", "stmt_id": "/app/Http/Controllers/HomeController.php:89", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('183')", "type": "query", "params": [], "bindings": ["183"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 89}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13821, "duration_str": "138.21ms", "stmt_id": "/app/Http/Controllers/HomeController.php:89", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('4094')", "type": "query", "params": [], "bindings": ["4094"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 89}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13591999999999999, "duration_str": "135.92ms", "stmt_id": "/app/Http/Controllers/HomeController.php:89", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` in ('53', '66') and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "53", "66"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 95}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15578, "duration_str": "155.78ms", "stmt_id": "/app/Http/Controllers/HomeController.php:95", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '57' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "57"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 101}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1368, "duration_str": "136.8ms", "stmt_id": "/app/Http/Controllers/HomeController.php:101", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` in ('61', '64') and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "61", "64"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 107}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14594, "duration_str": "145.94ms", "stmt_id": "/app/Http/Controllers/HomeController.php:107", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` in ('55', '60') and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "55", "60"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 113}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16515000000000002, "duration_str": "165.15ms", "stmt_id": "/app/Http/Controllers/HomeController.php:113", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` in ('68', '69') and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "68", "69"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 119}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.138, "duration_str": "138ms", "stmt_id": "/app/Http/Controllers/HomeController.php:119", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '56' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "56"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 125}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14045, "duration_str": "140.45ms", "stmt_id": "/app/Http/Controllers/HomeController.php:125", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '59' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "59"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 131}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13859, "duration_str": "138.59ms", "stmt_id": "/app/Http/Controllers/HomeController.php:131", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '58' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "58"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 137}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13772, "duration_str": "137.72ms", "stmt_id": "/app/Http/Controllers/HomeController.php:137", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '76' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "76"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 143}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14023, "duration_str": "140.23ms", "stmt_id": "/app/Http/Controllers/HomeController.php:143", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '78' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "78"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 149}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14142, "duration_str": "141.42ms", "stmt_id": "/app/Http/Controllers/HomeController.php:149", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '84' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "84"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14024, "duration_str": "140.24ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('84')", "type": "query", "params": [], "bindings": ["84"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13683, "duration_str": "136.83ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('72')", "type": "query", "params": [], "bindings": ["72"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13902, "duration_str": "139.02ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('4094')", "type": "query", "params": [], "bindings": ["4094"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14049, "duration_str": "140.49ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `pe_it_sorts` where `pe_it_sorts`.`activity_id` in ('455509')", "type": "query", "params": [], "bindings": ["455509"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16136, "duration_str": "161.36ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `peit_inability_sorts` where `peit_inability_sorts`.`pe_it_sort_id` in ('524')", "type": "query", "params": [], "bindings": ["524"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 22, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 154}, {"index": 30, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 33, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 91, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14762999999999998, "duration_str": "147.63ms", "stmt_id": "/app/Http/Controllers/HomeController.php:154", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '86' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "86"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13962, "duration_str": "139.62ms", "stmt_id": "/app/Http/Controllers/HomeController.php:160", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '87' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "87"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 165}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13898, "duration_str": "138.98ms", "stmt_id": "/app/Http/Controllers/HomeController.php:165", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '89' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "89"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 170}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1397, "duration_str": "139.7ms", "stmt_id": "/app/Http/Controllers/HomeController.php:170", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '90' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "90"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 175}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1385, "duration_str": "138.5ms", "stmt_id": "/app/Http/Controllers/HomeController.php:175", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '91' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "91"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13738999999999998, "duration_str": "137.39ms", "stmt_id": "/app/Http/Controllers/HomeController.php:180", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('91')", "type": "query", "params": [], "bindings": ["91"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 180}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1362, "duration_str": "136.2ms", "stmt_id": "/app/Http/Controllers/HomeController.php:180", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('102', '103')", "type": "query", "params": [], "bindings": ["102", "103"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 180}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13688999999999998, "duration_str": "136.89ms", "stmt_id": "/app/Http/Controllers/HomeController.php:180", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('4094')", "type": "query", "params": [], "bindings": ["4094"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 180}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13824, "duration_str": "138.24ms", "stmt_id": "/app/Http/Controllers/HomeController.php:180", "connection": "ebdb"}, {"sql": "select * from `pe_recognition_expenses` where `pe_recognition_expenses`.`activity_id` in ('424823', '424922')", "type": "query", "params": [], "bindings": ["424823", "424922"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 180}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14972, "duration_str": "149.72ms", "stmt_id": "/app/Http/Controllers/HomeController.php:180", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '92' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "92"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 185}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14113, "duration_str": "141.13ms", "stmt_id": "/app/Http/Controllers/HomeController.php:185", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '93' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "93"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 190}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14246999999999999, "duration_str": "142.47ms", "stmt_id": "/app/Http/Controllers/HomeController.php:190", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '94' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "94"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 195}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14022, "duration_str": "140.22ms", "stmt_id": "/app/Http/Controllers/HomeController.php:195", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '95' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "95"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 201}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14123, "duration_str": "141.23ms", "stmt_id": "/app/Http/Controllers/HomeController.php:201", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '97' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "97"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 207}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14178, "duration_str": "141.78ms", "stmt_id": "/app/Http/Controllers/HomeController.php:207", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '98' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "98"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 212}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1428, "duration_str": "142.8ms", "stmt_id": "/app/Http/Controllers/HomeController.php:212", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '83' and `state_id` = '63' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "83", "63"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 220}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14305, "duration_str": "143.05ms", "stmt_id": "/app/Http/Controllers/HomeController.php:220", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` in ('83')", "type": "query", "params": [], "bindings": ["83"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 220}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13909, "duration_str": "139.09ms", "stmt_id": "/app/Http/Controllers/HomeController.php:220", "connection": "ebdb"}, {"sql": "select * from `states` where `states`.`id` in ('63')", "type": "query", "params": [], "bindings": ["63"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 220}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15904, "duration_str": "159.04ms", "stmt_id": "/app/Http/Controllers/HomeController.php:220", "connection": "ebdb"}, {"sql": "select * from `users` where `users`.`id` in ('4094')", "type": "query", "params": [], "bindings": ["4094"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 220}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13898, "duration_str": "138.98ms", "stmt_id": "/app/Http/Controllers/HomeController.php:220", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '100' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "100"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 226}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14014, "duration_str": "140.14ms", "stmt_id": "/app/Http/Controllers/HomeController.php:226", "connection": "ebdb"}, {"sql": "select * from `activities` where `affiliate_id` = '1228962' and `service_id` = '102' and `activities`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1228962", "102"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 232}, {"index": 20, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 23, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 60, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 81, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14026, "duration_str": "140.26ms", "stmt_id": "/app/Http/Controllers/HomeController.php:232", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '79' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["79", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 235}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14324, "duration_str": "143.24ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '74' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["74", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 412}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1447, "duration_str": "144.7ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '75' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["75", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 480}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13718, "duration_str": "137.18ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '76' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["76", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 603}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13958, "duration_str": "139.58ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '77' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["77", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 709}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1375, "duration_str": "137.5ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '78' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["78", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 786}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13721, "duration_str": "137.21ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '79' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["79", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 864}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13948, "duration_str": "139.48ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '80' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["80", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 948}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13894, "duration_str": "138.94ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '83' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["83", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1030}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13833, "duration_str": "138.33ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '424821' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424821"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 784}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1041}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1379, "duration_str": "137.9ms", "stmt_id": "/app/Activity.php:784", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 789}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1041}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13615, "duration_str": "136.15ms", "stmt_id": "/app/Activity.php:789", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '421513' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 794}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1041}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14468999999999999, "duration_str": "144.69ms", "stmt_id": "/app/Activity.php:794", "connection": "ebdb"}, {"sql": "select * from `policy_sorts` where `policy_sorts`.`activity_id` = '421513' and `policy_sorts`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": null, "name": "/app/Activity.php", "line": 797}, {"index": 20, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1041}, {"index": 26, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 33, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 38, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 41, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 84, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 99, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13923, "duration_str": "139.23ms", "stmt_id": "/app/Activity.php:797", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '424821' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424821"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 803}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1042}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1408, "duration_str": "140.8ms", "stmt_id": "/app/Activity.php:803", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 808}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1042}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14581, "duration_str": "145.81ms", "stmt_id": "/app/Activity.php:808", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `activity_id` = '424820' limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Activity.php", "line": 812}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1042}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14043, "duration_str": "140.43ms", "stmt_id": "/app/Activity.php:812", "connection": "ebdb"}, {"sql": "select * from `medical_services_sort` where `medical_services_sort`.`activity_id` = '424821' and `medical_services_sort`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["424821"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1087}, {"index": 24, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 31, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 36, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 39, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 97, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15164, "duration_str": "151.64ms", "stmt_id": "view::affiliate.affiliate_profile:1087", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `affiliates`.`id` = '1228962' and `affiliates`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1228962"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1105}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14132, "duration_str": "141.32ms", "stmt_id": "view::affiliate.affiliate_profile:1105", "connection": "ebdb"}, {"sql": "select count(*) as aggregate from `activity_actions` where `activity_id` = '424820' and `new_state_id` = '160' and `activity_actions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["424820", "160"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/GisSort.php", "line": 304}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1148}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14135, "duration_str": "141.35ms", "stmt_id": "/app/GisSort.php:304", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '98' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["98", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1168}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13941, "duration_str": "139.41ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '84' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["84", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1301}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14219, "duration_str": "142.19ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '86' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["86", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1383}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16144999999999998, "duration_str": "161.45ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '87' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["87", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1468}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14288, "duration_str": "142.88ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '88' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["88", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1606}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14246999999999999, "duration_str": "142.47ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `affiliates`.`id` = '1228962' and `affiliates`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1228962"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1622}, {"index": 24, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 31, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 36, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 39, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 97, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13881, "duration_str": "138.81ms", "stmt_id": "view::affiliate.affiliate_profile:1622", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `gis_sort`.`activity_id` = '424820' and `gis_sort`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1651}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14036, "duration_str": "140.36ms", "stmt_id": "view::affiliate.affiliate_profile:1651", "connection": "ebdb"}, {"sql": "select count(*) as aggregate from `activity_actions` where `activity_id` = '424820' and `new_state_id` = '160' and `activity_actions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["424820", "160"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/GisSort.php", "line": 304}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1658}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13997, "duration_str": "139.97ms", "stmt_id": "/app/GisSort.php:304", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '89' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["89", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1683}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1466, "duration_str": "146.6ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '90' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["90", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1767}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1418, "duration_str": "141.8ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '91' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["91", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1856}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15966999999999998, "duration_str": "159.67ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14307, "duration_str": "143.07ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `gis_sort`.`activity_id` = '424820' and `gis_sort`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13897, "duration_str": "138.97ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1395, "duration_str": "139.5ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '421513' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13878000000000001, "duration_str": "138.78ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `policy_sorts` where `policy_sorts`.`activity_id` = '421513' and `policy_sorts`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13822, "duration_str": "138.22ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13772, "duration_str": "137.72ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `gis_sort`.`activity_id` = '424820' and `gis_sort`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.155, "duration_str": "155ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '424820' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14458000000000001, "duration_str": "144.58ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` = '421513' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13759, "duration_str": "137.59ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select * from `policy_sorts` where `policy_sorts`.`activity_id` = '421513' and `policy_sorts`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 19, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1925}, {"index": 25, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 32, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 37, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 40, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 74, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 83, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 98, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13917, "duration_str": "139.17ms", "stmt_id": "view::affiliate.affiliate_profile:1925", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '92' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["92", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 1943}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14301, "duration_str": "143.01ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '93' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["93", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2059}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13832, "duration_str": "138.32ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '94' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["94", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2128}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16401, "duration_str": "164.01ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '95' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["95", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2229}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15553, "duration_str": "155.53ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '97' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["97", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2298}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14159, "duration_str": "141.59ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '100' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["100", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2387}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13918, "duration_str": "139.18ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `services`.`id` = '102' and `ap`.`area_id` = '1' and `ap`.`view` = '1' and `p`.`name` = 'TRAMITE' limit 1", "type": "query", "params": [], "bindings": ["102", "1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/User.php", "line": 389}, {"index": 14, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2464}, {"index": 20, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15458000000000002, "duration_str": "154.58ms", "stmt_id": "/app/User.php:389", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `ap`.`area_id` = '1' and `ap`.`edit` = '1' and `p`.`name` = 'TRAMITE'", "type": "query", "params": [], "bindings": ["1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/User.php", "line": 404}, {"index": 13, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2537}, {"index": 19, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 26, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 31, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 34, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 92, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14165, "duration_str": "141.65ms", "stmt_id": "/app/User.php:404", "connection": "ebdb"}, {"sql": "select `services`.* from `services` left join `permissions` as `p` on `services`.`id` = `p`.`service_id` left join `area_permissions` as `ap` on `p`.`id` = `ap`.`permission_id` where `ap`.`area_id` = '1' and `ap`.`edit` = '1' and `p`.`name` = 'TRAMITE'", "type": "query", "params": [], "bindings": ["1", "1", "TRAMITE"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 12, "namespace": null, "name": "/app/User.php", "line": 404}, {"index": 13, "namespace": "view", "name": "affiliate.affiliate_profile", "line": 2553}, {"index": 19, "namespace": "view", "name": "affiliate", "line": 10}, {"index": 26, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 31, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 34, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 92, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16862, "duration_str": "168.62ms", "stmt_id": "/app/User.php:404", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 6}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13525, "duration_str": "135.25ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 8}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13699, "duration_str": "136.99ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 9}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13874, "duration_str": "138.74ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 668}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13549, "duration_str": "135.49ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 669}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1415, "duration_str": "141.5ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 671}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14019, "duration_str": "140.19ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 429}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 682}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15102000000000002, "duration_str": "151.02ms", "stmt_id": "/app/User.php:429", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 437}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 689}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16156, "duration_str": "161.56ms", "stmt_id": "/app/User.php:437", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 433}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 695}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14075, "duration_str": "140.75ms", "stmt_id": "/app/User.php:433", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 543}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 702}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15952000000000002, "duration_str": "159.52ms", "stmt_id": "/app/User.php:543", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 425}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 714}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13897, "duration_str": "138.97ms", "stmt_id": "/app/User.php:425", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 441}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 729}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14107, "duration_str": "141.07ms", "stmt_id": "/app/User.php:441", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 445}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 735}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13771, "duration_str": "137.71ms", "stmt_id": "/app/User.php:445", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 449}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 741}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13947, "duration_str": "139.47ms", "stmt_id": "/app/User.php:449", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 453}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 747}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1388, "duration_str": "138.8ms", "stmt_id": "/app/User.php:453", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 513}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 755}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13762, "duration_str": "137.62ms", "stmt_id": "/app/User.php:513", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 517}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 762}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13659000000000002, "duration_str": "136.59ms", "stmt_id": "/app/User.php:517", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 457}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 775}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13965, "duration_str": "139.65ms", "stmt_id": "/app/User.php:457", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 465}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 781}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13936, "duration_str": "139.36ms", "stmt_id": "/app/User.php:465", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 469}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 787}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14008, "duration_str": "140.08ms", "stmt_id": "/app/User.php:469", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 473}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 793}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13646, "duration_str": "136.46ms", "stmt_id": "/app/User.php:473", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 477}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 799}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13812, "duration_str": "138.12ms", "stmt_id": "/app/User.php:477", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 481}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 809}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13848, "duration_str": "138.48ms", "stmt_id": "/app/User.php:481", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 505}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 827}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13801, "duration_str": "138.01ms", "stmt_id": "/app/User.php:505", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 509}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 833}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13911, "duration_str": "139.11ms", "stmt_id": "/app/User.php:509", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 601}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 845}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13846, "duration_str": "138.46ms", "stmt_id": "/app/User.php:601", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 461}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 867}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13703, "duration_str": "137.03ms", "stmt_id": "/app/User.php:461", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 526}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 886}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13776, "duration_str": "137.76ms", "stmt_id": "/app/User.php:526", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 890}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13806000000000002, "duration_str": "138.06ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 895}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13811, "duration_str": "138.11ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 901}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13747, "duration_str": "137.47ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 907}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13878000000000001, "duration_str": "138.78ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 913}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13921, "duration_str": "139.21ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 918}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14037, "duration_str": "140.37ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 923}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13728, "duration_str": "137.28ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 928}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14108, "duration_str": "141.08ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 933}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14846, "duration_str": "148.46ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 938}, {"index": 23, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14379, "duration_str": "143.79ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1110}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16066999999999998, "duration_str": "160.67ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1116}, {"index": 21, "namespace": "view", "name": "affiliate", "line": 13}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13824, "duration_str": "138.24ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}]}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"wilmer<PERSON>za10\"\n  \"user\" => array:50 [\n    \"id\" => 18\n    \"user_type\" => \"\"\n    \"email\" => \"<EMAIL>\"\n    \"affiliate_id\" => null\n    \"created_at\" => \"2024-09-17 10:51:15\"\n    \"updated_at\" => \"2025-07-21 15:22:52\"\n    \"full_name\" => \"WILMERR MAZA BANDA\"\n    \"first_name\" => \"WILMERR\"\n    \"last_name\" => \"MAZA BANDA\"\n    \"username\" => \"wilmermaza10\"\n    \"photo\" => \"user_photo/at8RH2yiiVxOjsYpnJozCU3S7UrqTy5O4T49x4i8.jpeg\"\n    \"area_id\" => 1\n    \"position_id\" => null\n    \"deleted_at\" => null\n    \"ascribed\" => 0\n    \"old_password1\" => \"$2y$10$cFzxSS3xlT4izkkAK7kze.tBRQ7Jm9N6uh1D4r8gCrLH39uAyhljq\"\n    \"old_password2\" => \"$2y$10$sLHd.4zpr6JC9myNAorkHOxRsO3Oys2y7SG0mbTvN/d5Bo3uYhUoS\"\n    \"old_password3\" => \"$2y$10$PSY9nL.qDVMLjriYKtLHoeh9rX1awTWv.y5aEsdmoUpfe.1rqGhnC\"\n    \"last_login\" => \"2025-07-21 15:22:52\"\n    \"next_update\" => \"2025-08-01 10:24:44\"\n    \"extra_data\" => \"\"\n    \"zoom_api_key\" => null\n    \"zoom_api_secret\" => null\n    \"identification_number\" => \"**********\"\n    \"creator\" => null\n    \"has_schedule\" => \"0\"\n    \"company_id\" => null\n    \"department\" => null\n    \"municipality\" => null\n    \"ocupation\" => null\n    \"phone\" => \"**********\"\n    \"doc_type\" => \"CF\"\n    \"medical_record_number\" => null\n    \"license_number\" => null\n    \"rethus\" => null\n    \"signature\" => null\n    \"name_area\" => null\n    \"code_mnk\" => \"000010\"\n    \"brokerage_name\" => \"BCR CORREDORA DE SEGUROS SA\"\n    \"new_business_email\" => \"\"\n    \"advisor_name\" => \"000010\"\n    \"type_inter\" => \"G\"\n    \"correduria\" => null\n    \"code_correduria\" => \"009101\"\n    \"tipo_corredor\" => \"C\"\n    \"provider_id\" => null\n    \"resp_acsel_login\" => null\n    \"tomador_id\" => 0\n    \"unique_code\" => \"651827\"\n    \"autorizados\" => \"12486,42222\"\n  ]\n]", "api": "array:2 [\n  \"name\" => \"Guest\"\n  \"user\" => array:1 [\n    \"guest\" => true\n  ]\n]"}, "names": "web: wilmermaza10"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc", "_previous": "array:1 [\n  \"url\" => \"https://oceanica-qa.renapp.com/afiliado/1228962\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "18", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"format": "html", "content_type": "text/html; charset=UTF-8", "status_text": "OK", "status_code": "200", "request_query": "[]", "request_request": "[]", "request_headers": "array:18 [\n  \"cookie\" => array:1 [\n    0 => \"XSRF-TOKEN=eyJpdiI6ImxFQytyK2dqWUZVUmNEY2REb3dWSEE9PSIsInZhbHVlIjoiUisxaEN5d1BlZkF0SlwvbHBMcitRNDNvejhTYUhPcW0xSm9jdXBHU1l3NlBIRWdQa1YxbmlnVXlRV3l3U3hSNnkiLCJtYWMiOiI0M2E3ZjE2NGFmMmY4ZmQwNWY3ZmQ0MzBmNjdiMWM2NWMyZTdiY2IzMGU2MDAxNjJkMTkxZmQwYzA2MWViNWZmIn0%3D; laravel_session_cookie=eyJpdiI6IkRsUEVwTk92dFVNU2ZZVStXMDNzWHc9PSIsInZhbHVlIjoiNUlVNXhrTmVcL3FJWUJkU0I3MWhOeStCN1IrWFVpZmp1VGxnSXZOTGJWNnJJb1pPbVlkQ0pIbnM3bnNJT1g0OG1XZ25aUVhCWTFBbmt3c3YzNkFCV0IyOGR2RGxFVVZtam13Y2Y1d0NYSWlZNmZCS2c3M1daem1WZVMzS2NMbW5IIiwibWFjIjoiMDY4MGI2NGZmMDA2ODY5ODViZTFlYWNiOThiMmRiYWU3ZmU4ZTMwYjg0ZjExZDU1YWIzZGI2YmJkNTlhMzYyNyJ9\"\n  ]\n  \"accept-language\" => array:1 [\n    0 => \"en,es-ES;q=0.9,es;q=0.8\"\n  ]\n  \"accept-encoding\" => array:1 [\n    0 => \"gzip, deflate, br, zstd\"\n  ]\n  \"referer\" => array:1 [\n    0 => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  ]\n  \"sec-fetch-dest\" => array:1 [\n    0 => \"document\"\n  ]\n  \"sec-fetch-user\" => array:1 [\n    0 => \"?1\"\n  ]\n  \"sec-fetch-mode\" => array:1 [\n    0 => \"navigate\"\n  ]\n  \"sec-fetch-site\" => array:1 [\n    0 => \"same-origin\"\n  ]\n  \"accept\" => array:1 [\n    0 => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  ]\n  \"user-agent\" => array:1 [\n    0 => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  ]\n  \"upgrade-insecure-requests\" => array:1 [\n    0 => \"1\"\n  ]\n  \"sec-ch-ua-platform\" => array:1 [\n    0 => \"\"Windows\"\"\n  ]\n  \"sec-ch-ua-mobile\" => array:1 [\n    0 => \"?0\"\n  ]\n  \"sec-ch-ua\" => array:1 [\n    0 => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  ]\n  \"connection\" => array:1 [\n    0 => \"keep-alive\"\n  ]\n  \"host\" => array:1 [\n    0 => \"oceanica-qa.renapp.com\"\n  ]\n  \"content-length\" => array:1 [\n    0 => \"\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"\"\n  ]\n]", "request_server": "array:62 [\n  \"PHP_EXTRA_CONFIGURE_ARGS\" => \"--enable-fpm --with-fpm-user=www-data --with-fpm-group=www-data --disable-cgi\"\n  \"LANGUAGE\" => \"es_ES.UTF-8\"\n  \"HOSTNAME\" => \"b44486792d1f\"\n  \"PHP_INI_DIR\" => \"/usr/local/etc/php\"\n  \"HOME\" => \"/var/www\"\n  \"PHP_LDFLAGS\" => \"-Wl,-O1 -pie\"\n  \"PHP_CFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_VERSION\" => \"7.2.34\"\n  \"GPG_KEYS\" => \"1729F83938DA44E27BA0F4D3DBDB397470D12172 B1B44D8F021E4E2D6021E995DC9FF8D3EE5AF27F\"\n  \"PHP_CPPFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_ASC_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz.asc\"\n  \"PHP_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz\"\n  \"PATH\" => \"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"\n  \"LANG\" => \"es_ES.UTF-8\"\n  \"PHPIZE_DEPS\" => \"autoconf \\t\\tdpkg-dev \\t\\tfile \\t\\tg++ \\t\\tgcc \\t\\tlibc-dev \\t\\tmake \\t\\tpkg-config \\t\\tre2c\"\n  \"LC_ALL\" => \"es_ES.UTF-8\"\n  \"PWD\" => \"/var/www/html\"\n  \"PHP_SHA256\" => \"409e11bc6a2c18707dfc44bc61c820ddfd81e17481470f3405ee7822d8379903\"\n  \"USER\" => \"www-data\"\n  \"HTTP_COOKIE\" => \"XSRF-TOKEN=eyJpdiI6ImxFQytyK2dqWUZVUmNEY2REb3dWSEE9PSIsInZhbHVlIjoiUisxaEN5d1BlZkF0SlwvbHBMcitRNDNvejhTYUhPcW0xSm9jdXBHU1l3NlBIRWdQa1YxbmlnVXlRV3l3U3hSNnkiLCJtYWMiOiI0M2E3ZjE2NGFmMmY4ZmQwNWY3ZmQ0MzBmNjdiMWM2NWMyZTdiY2IzMGU2MDAxNjJkMTkxZmQwYzA2MWViNWZmIn0%3D; laravel_session_cookie=eyJpdiI6IkRsUEVwTk92dFVNU2ZZVStXMDNzWHc9PSIsInZhbHVlIjoiNUlVNXhrTmVcL3FJWUJkU0I3MWhOeStCN1IrWFVpZmp1VGxnSXZOTGJWNnJJb1pPbVlkQ0pIbnM3bnNJT1g0OG1XZ25aUVhCWTFBbmt3c3YzNkFCV0IyOGR2RGxFVVZtam13Y2Y1d0NYSWlZNmZCS2c3M1daem1WZVMzS2NMbW5IIiwibWFjIjoiMDY4MGI2NGZmMDA2ODY5ODViZTFlYWNiOThiMmRiYWU3ZmU4ZTMwYjg0ZjExZDU1YWIzZGI2YmJkNTlhMzYyNyJ9\"\n  \"HTTP_ACCEPT_LANGUAGE\" => \"en,es-ES;q=0.9,es;q=0.8\"\n  \"HTTP_ACCEPT_ENCODING\" => \"gzip, deflate, br, zstd\"\n  \"HTTP_REFERER\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  \"HTTP_SEC_FETCH_DEST\" => \"document\"\n  \"HTTP_SEC_FETCH_USER\" => \"?1\"\n  \"HTTP_SEC_FETCH_MODE\" => \"navigate\"\n  \"HTTP_SEC_FETCH_SITE\" => \"same-origin\"\n  \"HTTP_ACCEPT\" => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  \"HTTP_USER_AGENT\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  \"HTTP_UPGRADE_INSECURE_REQUESTS\" => \"1\"\n  \"HTTP_SEC_CH_UA_PLATFORM\" => \"\"Windows\"\"\n  \"HTTP_SEC_CH_UA_MOBILE\" => \"?0\"\n  \"HTTP_SEC_CH_UA\" => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  \"HTTP_CONNECTION\" => \"keep-alive\"\n  \"HTTP_HOST\" => \"oceanica-qa.renapp.com\"\n  \"PATH_INFO\" => \"\"\n  \"SCRIPT_FILENAME\" => \"/var/www/html/public/index.php\"\n  \"REDIRECT_STATUS\" => \"200\"\n  \"SERVER_NAME\" => \"oceanica.renapp.us\"\n  \"SERVER_PORT\" => \"443\"\n  \"SERVER_ADDR\" => \"**********\"\n  \"REMOTE_PORT\" => \"56108\"\n  \"REMOTE_ADDR\" => \"**********\"\n  \"SERVER_SOFTWARE\" => \"nginx/1.26.3\"\n  \"GATEWAY_INTERFACE\" => \"CGI/1.1\"\n  \"HTTPS\" => \"on\"\n  \"REQUEST_SCHEME\" => \"https\"\n  \"SERVER_PROTOCOL\" => \"HTTP/1.1\"\n  \"DOCUMENT_ROOT\" => \"/var/www/html/public\"\n  \"DOCUMENT_URI\" => \"/index.php\"\n  \"REQUEST_URI\" => \"/afiliado/1228962\"\n  \"SCRIPT_NAME\" => \"/index.php\"\n  \"CONTENT_LENGTH\" => \"\"\n  \"CONTENT_TYPE\" => \"\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"QUERY_STRING\" => \"\"\n  \"FCGI_ROLE\" => \"RESPONDER\"\n  \"PHP_SELF\" => \"/index.php\"\n  \"REQUEST_TIME_FLOAT\" => 1753277446.6867\n  \"REQUEST_TIME\" => 1753277446\n  \"argv\" => []\n  \"argc\" => 0\n]", "request_cookies": "array:2 [\n  \"XSRF-TOKEN\" => null\n  \"laravel_session_cookie\" => \"BJvB71yW4W7wGKpeFrH8iWn6wGY1p14rZv9U2owd\"\n]", "response_headers": "array:5 [\n  \"cache-control\" => array:1 [\n    0 => \"no-cache, private\"\n  ]\n  \"date\" => array:1 [\n    0 => \"Wed, 23 Jul 2025 13:31:03 GMT\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"text/html; charset=UTF-8\"\n  ]\n  \"set-cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IktsaFFGMnRPeUJGVE1pMmZ3SzM5bkE9PSIsInZhbHVlIjoiRjVJWk03a2E0aDRtaWw0MUoyZ0FPWGZ4TjNtc21kSndpYmIxYTA2Wml2R2dwK0k3c3JRa0R5dFEzWGY0MmliUSIsIm1hYyI6ImY5MzJjZjk5ZmQwZjZkNjFkMmIzNTIyNzcwZjY3YTc3MTlmOGRhZjg1M2IzNjhiMmFlMjg4MjM4YjUwZGNjZDQifQ%3D%3D; expires=Thu, 24-Jul-2025 09:31:17 GMT; Max-Age=72000; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6ImZBbm13NkhSRUtEOWE4STlrM2NxaEE9PSIsInZhbHVlIjoiYklMTHFFckVKaEhKTmlDWHFZc2M5cXZRWDNcL05pcSs3TDNYb1wvbWZCSW1BUnJnOWFrZ2dWa3BWUld2Qm93VlBsaHVjYUN6cURGakFES1BPR083Skh2dFhYXC9XRlllY1lPV25DSzVUa3FNZ3B2bmIrc3U1XC9cLzZad21cL1BtZEFYcTkiLCJtYWMiOiI0MDcwZjUzODRjOTU5NzBhOTg4YWQyYmM2NzE5NGNkN2RmNzY3NDI2M2ZiYmVjYWYwMDc0MThiNmFkMTNkYzY1In0%3D; expires=Thu, 24-Jul-2025 09:31:17 GMT; Max-Age=72000; path=/; httponly\"\n  ]\n  \"Set-Cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IktsaFFGMnRPeUJGVE1pMmZ3SzM5bkE9PSIsInZhbHVlIjoiRjVJWk03a2E0aDRtaWw0MUoyZ0FPWGZ4TjNtc21kSndpYmIxYTA2Wml2R2dwK0k3c3JRa0R5dFEzWGY0MmliUSIsIm1hYyI6ImY5MzJjZjk5ZmQwZjZkNjFkMmIzNTIyNzcwZjY3YTc3MTlmOGRhZjg1M2IzNjhiMmFlMjg4MjM4YjUwZGNjZDQifQ%3D%3D; expires=Thu, 24-Jul-2025 09:31:17 GMT; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6ImZBbm13NkhSRUtEOWE4STlrM2NxaEE9PSIsInZhbHVlIjoiYklMTHFFckVKaEhKTmlDWHFZc2M5cXZRWDNcL05pcSs3TDNYb1wvbWZCSW1BUnJnOWFrZ2dWa3BWUld2Qm93VlBsaHVjYUN6cURGakFES1BPR083Skh2dFhYXC9XRlllY1lPV25DSzVUa3FNZ3B2bmIrc3U1XC9cLzZad21cL1BtZEFYcTkiLCJtYWMiOiI0MDcwZjUzODRjOTU5NzBhOTg4YWQyYmM2NzE5NGNkN2RmNzY3NDI2M2ZiYmVjYWYwMDc0MThiNmFkMTNkYzY1In0%3D; expires=Thu, 24-Jul-2025 09:31:17 GMT; path=/; httponly\"\n  ]\n]", "path_info": "/afiliado/1228962", "session_attributes": "array:5 [\n  \"_token\" => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  \"_previous\" => array:1 [\n    \"url\" => \"https://oceanica-qa.renapp.com/afiliado/1228962\"\n  ]\n  \"_flash\" => array:2 [\n    \"old\" => []\n    \"new\" => []\n  ]\n  \"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d\" => 18\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n]"}}