{"__meta": {"id": "909b1ead62458e2b9a2d392e63c836e1", "datetime": "2025-07-23 07:54:35", "utime": 1753278875.705507, "method": "GET", "uri": "/servicio/455752/subrogacion", "ip": "**********"}, "php": {"version": "7.2.34", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753278860.901421, "end": 1753278875.705528, "duration": 14.804106950759888, "duration_str": "14.8s", "measures": [{"label": "Booting", "start": 1753278860.901421, "relative_start": 0, "end": 1753278866.319709, "relative_end": 1753278866.319709, "duration": 5.418287992477417, "duration_str": "5.42s", "params": [], "collector": null}, {"label": "Application", "start": 1753278865.708053, "relative_start": 4.806632041931152, "end": 1753278875.705529, "relative_end": 9.5367431640625e-07, "duration": 9.997475862503052, "duration_str": "10s", "params": [], "collector": null}]}, "memory": {"peak_usage": 2097152, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "services.subrogacion.form.form (resources/views/services/subrogacion/form/form.blade.php)", "param_count": 4, "params": ["activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}, {"name": "services.subrogacion.form.components.costos_derivados (resources/views/services/subrogacion/form/components/costos_derivados.blade.php)", "param_count": 177, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}, {"name": "services.subrogacion.form.components.datos_demandado (resources/views/services/subrogacion/form/components/datos_demandado.blade.php)", "param_count": 177, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}, {"name": "services.subrogacion.form.components.caso_judiciales (resources/views/services/subrogacion/form/components/caso_judiciales.blade.php)", "param_count": 177, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}, {"name": "services.subrogacion.form.components.estado_cuenta (resources/views/services/subrogacion/form/components/estado_cuenta.blade.php)", "param_count": 177, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}, {"name": "layouts.main (resources/views/layouts/main.blade.php)", "param_count": 177, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "subrogacionSort", "activityGis", "activityPolicy"], "type": "blade"}]}, "route": {"uri": "GET servicio/{id}/subrogacion", "middleware": "web, App\\Http\\Middleware\\CheckClientAccess, renew.password", "domain": "{cpath}.renapp.com", "controller": "App\\Http\\Controllers\\Services\\SubrogacionSortController@form", "namespace": "App\\Http\\Controllers", "prefix": null, "where": [], "file": "app/Http/Controllers/Services/SubrogacionSortController.php:29-47"}, "queries": {"nb_statements": 52, "nb_failed_statements": 0, "accumulated_duration": 7.***************, "accumulated_duration_str": "7.35s", "statements": [{"sql": "select * from `users` where `id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 46, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 49, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 70, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13582, "duration_str": "135.82ms", "stmt_id": "/app/Http/Middleware/RequestLoggerMiddleware.php:28", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 22}, {"index": 48, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 51, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 72, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13736, "duration_str": "137.36ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:22", "connection": "ebdb"}, {"sql": "select * from `user_clients` where `client_id` = '3' and `user_id` = '18' limit 1", "type": "query", "params": [], "bindings": ["3", "18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 25}, {"index": 47, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 50, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 71, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13607, "duration_str": "136.07ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:25", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '455752' and `service_id` = '101' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["455752", "101"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14193, "duration_str": "141.93ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `subrogacion_sort` where `subrogacion_sort`.`activity_id` in ('455752')", "type": "query", "params": [], "bindings": ["455752"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15619, "duration_str": "156.19ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `affiliates`.`id` in ('1228962') and `affiliates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1228962"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14286000000000001, "duration_str": "142.86ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` in ('424820') and `activities`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15480000000000002, "duration_str": "154.8ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `gis_sort`.`activity_id` in ('424820')", "type": "query", "params": [], "bindings": ["424820"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 23, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 31, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 34, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 92, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.20676, "duration_str": "206.76ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `activities` where `activities`.`id` in ('421513') and `activities`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 23, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 31, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 34, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 77, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 92, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13662, "duration_str": "136.62ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select * from `policy_sorts` where `policy_sorts`.`activity_id` in ('421513')", "type": "query", "params": [], "bindings": ["421513"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 28, "namespace": null, "name": "/app/Http/Controllers/Services/SubrogacionSortController.php", "line": 34}, {"index": 36, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 39, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 82, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 97, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13591999999999999, "duration_str": "135.92ms", "stmt_id": "/app/Http/Controllers/Services/SubrogacionSortController.php:34", "connection": "ebdb"}, {"sql": "select count(*) as aggregate from `activity_actions` where `activity_id` = '424820' and `new_state_id` = '160' and `activity_actions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["424820", "160"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/GisSort.php", "line": 287}, {"index": 14, "namespace": "view", "name": "services.subrogacion.form.form", "line": 43}, {"index": 21, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13731000000000002, "duration_str": "137.31ms", "stmt_id": "/app/GisSort.php:287", "connection": "ebdb"}, {"sql": "select * from `providers` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/GisSort.php", "line": 312}, {"index": 14, "namespace": "view", "name": "services.subrogacion.form.form", "line": 49}, {"index": 21, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13792, "duration_str": "137.92ms", "stmt_id": "/app/GisSort.php:312", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 6}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13806000000000002, "duration_str": "138.06ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 8}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13977, "duration_str": "139.77ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 9}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14512, "duration_str": "145.12ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 668}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13779, "duration_str": "137.79ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 669}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13585, "duration_str": "135.85ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 671}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13748, "duration_str": "137.48ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 429}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 682}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13812, "duration_str": "138.12ms", "stmt_id": "/app/User.php:429", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 437}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 689}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1377, "duration_str": "137.7ms", "stmt_id": "/app/User.php:437", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 433}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 695}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13855, "duration_str": "138.55ms", "stmt_id": "/app/User.php:433", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 543}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 702}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1369, "duration_str": "136.9ms", "stmt_id": "/app/User.php:543", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 425}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 714}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1437, "duration_str": "143.7ms", "stmt_id": "/app/User.php:425", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 441}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 729}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13683, "duration_str": "136.83ms", "stmt_id": "/app/User.php:441", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 445}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 735}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14074, "duration_str": "140.74ms", "stmt_id": "/app/User.php:445", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 449}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 741}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14041, "duration_str": "140.41ms", "stmt_id": "/app/User.php:449", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 453}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 747}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13638999999999998, "duration_str": "136.39ms", "stmt_id": "/app/User.php:453", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 513}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 755}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13835, "duration_str": "138.35ms", "stmt_id": "/app/User.php:513", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 517}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 762}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13632, "duration_str": "136.32ms", "stmt_id": "/app/User.php:517", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 457}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 775}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.137, "duration_str": "137ms", "stmt_id": "/app/User.php:457", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 465}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 781}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13702, "duration_str": "137.02ms", "stmt_id": "/app/User.php:465", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 469}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 787}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13658, "duration_str": "136.58ms", "stmt_id": "/app/User.php:469", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 473}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 793}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13747, "duration_str": "137.47ms", "stmt_id": "/app/User.php:473", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 477}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 799}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13749, "duration_str": "137.49ms", "stmt_id": "/app/User.php:477", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 481}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 809}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16469, "duration_str": "164.69ms", "stmt_id": "/app/User.php:481", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 505}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 827}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13803, "duration_str": "138.03ms", "stmt_id": "/app/User.php:505", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 509}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 833}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13887, "duration_str": "138.87ms", "stmt_id": "/app/User.php:509", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 601}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 845}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13888999999999999, "duration_str": "138.89ms", "stmt_id": "/app/User.php:601", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 461}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 867}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13883, "duration_str": "138.83ms", "stmt_id": "/app/User.php:461", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 526}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 886}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14034, "duration_str": "140.34ms", "stmt_id": "/app/User.php:526", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 890}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13997, "duration_str": "139.97ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 895}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1376, "duration_str": "137.6ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 901}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13824, "duration_str": "138.24ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 907}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14232, "duration_str": "142.32ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 913}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14434, "duration_str": "144.34ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 918}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14037, "duration_str": "140.37ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 923}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13897, "duration_str": "138.97ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 928}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13829, "duration_str": "138.29ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 933}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14834999999999998, "duration_str": "148.35ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 576}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 938}, {"index": 23, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13944, "duration_str": "139.44ms", "stmt_id": "/app/User.php:576", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1110}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13705, "duration_str": "137.05ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1116}, {"index": 21, "namespace": "view", "name": "services.subrogacion.form.form", "line": 197}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13741, "duration_str": "137.41ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}]}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"wilmer<PERSON>za10\"\n  \"user\" => array:50 [\n    \"id\" => 18\n    \"user_type\" => \"\"\n    \"email\" => \"<EMAIL>\"\n    \"affiliate_id\" => null\n    \"created_at\" => \"2024-09-17 10:51:15\"\n    \"updated_at\" => \"2025-07-21 15:22:52\"\n    \"full_name\" => \"WILMERR MAZA BANDA\"\n    \"first_name\" => \"WILMERR\"\n    \"last_name\" => \"MAZA BANDA\"\n    \"username\" => \"wilmermaza10\"\n    \"photo\" => \"user_photo/at8RH2yiiVxOjsYpnJozCU3S7UrqTy5O4T49x4i8.jpeg\"\n    \"area_id\" => 1\n    \"position_id\" => null\n    \"deleted_at\" => null\n    \"ascribed\" => 0\n    \"old_password1\" => \"$2y$10$cFzxSS3xlT4izkkAK7kze.tBRQ7Jm9N6uh1D4r8gCrLH39uAyhljq\"\n    \"old_password2\" => \"$2y$10$sLHd.4zpr6JC9myNAorkHOxRsO3Oys2y7SG0mbTvN/d5Bo3uYhUoS\"\n    \"old_password3\" => \"$2y$10$PSY9nL.qDVMLjriYKtLHoeh9rX1awTWv.y5aEsdmoUpfe.1rqGhnC\"\n    \"last_login\" => \"2025-07-21 15:22:52\"\n    \"next_update\" => \"2025-08-01 10:24:44\"\n    \"extra_data\" => \"\"\n    \"zoom_api_key\" => null\n    \"zoom_api_secret\" => null\n    \"identification_number\" => \"**********\"\n    \"creator\" => null\n    \"has_schedule\" => \"0\"\n    \"company_id\" => null\n    \"department\" => null\n    \"municipality\" => null\n    \"ocupation\" => null\n    \"phone\" => \"**********\"\n    \"doc_type\" => \"CF\"\n    \"medical_record_number\" => null\n    \"license_number\" => null\n    \"rethus\" => null\n    \"signature\" => null\n    \"name_area\" => null\n    \"code_mnk\" => \"000010\"\n    \"brokerage_name\" => \"BCR CORREDORA DE SEGUROS SA\"\n    \"new_business_email\" => \"\"\n    \"advisor_name\" => \"000010\"\n    \"type_inter\" => \"G\"\n    \"correduria\" => null\n    \"code_correduria\" => \"009101\"\n    \"tipo_corredor\" => \"C\"\n    \"provider_id\" => null\n    \"resp_acsel_login\" => null\n    \"tomador_id\" => 0\n    \"unique_code\" => \"651827\"\n    \"autorizados\" => \"12486,42222\"\n  ]\n]", "api": "array:2 [\n  \"name\" => \"Guest\"\n  \"user\" => array:1 [\n    \"guest\" => true\n  ]\n]"}, "names": "web: wilmermaza10"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc", "_previous": "array:1 [\n  \"url\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "18", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"format": "html", "content_type": "text/html; charset=UTF-8", "status_text": "OK", "status_code": "200", "request_query": "[]", "request_request": "[]", "request_headers": "array:19 [\n  \"cookie\" => array:1 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IkpuMmxiSkF1Vm9GVFwvN1NMaGpqQjBnPT0iLCJ2YWx1ZSI6IldSTFg1VjBDSnVoY2I3RlBlbks0V09iUTNocHRLOHFhcUJ1UlZ6YXVcL1E0bzMyUHV4bGk2U0NPdXA1ajhTMldKIiwibWFjIjoiNjI4ZDczNmU1NjY4MTIwMTM1NTE5MmYxMzVlNGZhNzk0OGMxZjQ4YzVkZjMyMjAyNDUyMjY4Y2YzNmI2ODYzNSJ9; laravel_session_cookie=eyJpdiI6IlFHdklvcWNPcktEXC9IUkxLVUtNMmd3PT0iLCJ2YWx1ZSI6Ikt5Q3dkN1c2VjF4T1EwTE1nTzBEbjVyV0twVkxPWHRqUGxWZmV0SnNieWlQekR1dCtjZE5CWkNvajVvWmMwNWlFSlNLQncrakh6cDlYWnFLT0Y0SmtuYnRUZEc4b2l0YkNnQ3haakJPYldhT2RtTU5zTG5CWFljXC8wWlhOOHRnZyIsIm1hYyI6IjBiN2Q0NWI1MjU4YzFmOGZhNzAxYWFkYzBjMzAyMjAyZTU1MzAwNzA2NTE2N2ZmZjVhNWU2NGExZjJjMDM2YjgifQ%3D%3D\"\n  ]\n  \"accept-language\" => array:1 [\n    0 => \"en,es-ES;q=0.9,es;q=0.8\"\n  ]\n  \"accept-encoding\" => array:1 [\n    0 => \"gzip, deflate, br, zstd\"\n  ]\n  \"referer\" => array:1 [\n    0 => \"https://oceanica-qa.renapp.com/servicio/455752\"\n  ]\n  \"sec-fetch-dest\" => array:1 [\n    0 => \"document\"\n  ]\n  \"sec-fetch-user\" => array:1 [\n    0 => \"?1\"\n  ]\n  \"sec-fetch-mode\" => array:1 [\n    0 => \"navigate\"\n  ]\n  \"sec-fetch-site\" => array:1 [\n    0 => \"same-origin\"\n  ]\n  \"accept\" => array:1 [\n    0 => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  ]\n  \"user-agent\" => array:1 [\n    0 => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  ]\n  \"upgrade-insecure-requests\" => array:1 [\n    0 => \"1\"\n  ]\n  \"sec-ch-ua-platform\" => array:1 [\n    0 => \"\"Windows\"\"\n  ]\n  \"sec-ch-ua-mobile\" => array:1 [\n    0 => \"?0\"\n  ]\n  \"sec-ch-ua\" => array:1 [\n    0 => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  ]\n  \"cache-control\" => array:1 [\n    0 => \"max-age=0\"\n  ]\n  \"connection\" => array:1 [\n    0 => \"keep-alive\"\n  ]\n  \"host\" => array:1 [\n    0 => \"oceanica-qa.renapp.com\"\n  ]\n  \"content-length\" => array:1 [\n    0 => \"\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"\"\n  ]\n]", "request_server": "array:63 [\n  \"PHP_EXTRA_CONFIGURE_ARGS\" => \"--enable-fpm --with-fpm-user=www-data --with-fpm-group=www-data --disable-cgi\"\n  \"LANGUAGE\" => \"es_ES.UTF-8\"\n  \"HOSTNAME\" => \"b44486792d1f\"\n  \"PHP_INI_DIR\" => \"/usr/local/etc/php\"\n  \"HOME\" => \"/var/www\"\n  \"PHP_LDFLAGS\" => \"-Wl,-O1 -pie\"\n  \"PHP_CFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_VERSION\" => \"7.2.34\"\n  \"GPG_KEYS\" => \"1729F83938DA44E27BA0F4D3DBDB397470D12172 B1B44D8F021E4E2D6021E995DC9FF8D3EE5AF27F\"\n  \"PHP_CPPFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_ASC_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz.asc\"\n  \"PHP_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz\"\n  \"PATH\" => \"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"\n  \"LANG\" => \"es_ES.UTF-8\"\n  \"PHPIZE_DEPS\" => \"autoconf \\t\\tdpkg-dev \\t\\tfile \\t\\tg++ \\t\\tgcc \\t\\tlibc-dev \\t\\tmake \\t\\tpkg-config \\t\\tre2c\"\n  \"LC_ALL\" => \"es_ES.UTF-8\"\n  \"PWD\" => \"/var/www/html\"\n  \"PHP_SHA256\" => \"409e11bc6a2c18707dfc44bc61c820ddfd81e17481470f3405ee7822d8379903\"\n  \"USER\" => \"www-data\"\n  \"HTTP_COOKIE\" => \"XSRF-TOKEN=eyJpdiI6IkpuMmxiSkF1Vm9GVFwvN1NMaGpqQjBnPT0iLCJ2YWx1ZSI6IldSTFg1VjBDSnVoY2I3RlBlbks0V09iUTNocHRLOHFhcUJ1UlZ6YXVcL1E0bzMyUHV4bGk2U0NPdXA1ajhTMldKIiwibWFjIjoiNjI4ZDczNmU1NjY4MTIwMTM1NTE5MmYxMzVlNGZhNzk0OGMxZjQ4YzVkZjMyMjAyNDUyMjY4Y2YzNmI2ODYzNSJ9; laravel_session_cookie=eyJpdiI6IlFHdklvcWNPcktEXC9IUkxLVUtNMmd3PT0iLCJ2YWx1ZSI6Ikt5Q3dkN1c2VjF4T1EwTE1nTzBEbjVyV0twVkxPWHRqUGxWZmV0SnNieWlQekR1dCtjZE5CWkNvajVvWmMwNWlFSlNLQncrakh6cDlYWnFLT0Y0SmtuYnRUZEc4b2l0YkNnQ3haakJPYldhT2RtTU5zTG5CWFljXC8wWlhOOHRnZyIsIm1hYyI6IjBiN2Q0NWI1MjU4YzFmOGZhNzAxYWFkYzBjMzAyMjAyZTU1MzAwNzA2NTE2N2ZmZjVhNWU2NGExZjJjMDM2YjgifQ%3D%3D\"\n  \"HTTP_ACCEPT_LANGUAGE\" => \"en,es-ES;q=0.9,es;q=0.8\"\n  \"HTTP_ACCEPT_ENCODING\" => \"gzip, deflate, br, zstd\"\n  \"HTTP_REFERER\" => \"https://oceanica-qa.renapp.com/servicio/455752\"\n  \"HTTP_SEC_FETCH_DEST\" => \"document\"\n  \"HTTP_SEC_FETCH_USER\" => \"?1\"\n  \"HTTP_SEC_FETCH_MODE\" => \"navigate\"\n  \"HTTP_SEC_FETCH_SITE\" => \"same-origin\"\n  \"HTTP_ACCEPT\" => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  \"HTTP_USER_AGENT\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n  \"HTTP_UPGRADE_INSECURE_REQUESTS\" => \"1\"\n  \"HTTP_SEC_CH_UA_PLATFORM\" => \"\"Windows\"\"\n  \"HTTP_SEC_CH_UA_MOBILE\" => \"?0\"\n  \"HTTP_SEC_CH_UA\" => \"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"\"\n  \"HTTP_CACHE_CONTROL\" => \"max-age=0\"\n  \"HTTP_CONNECTION\" => \"keep-alive\"\n  \"HTTP_HOST\" => \"oceanica-qa.renapp.com\"\n  \"PATH_INFO\" => \"\"\n  \"SCRIPT_FILENAME\" => \"/var/www/html/public/index.php\"\n  \"REDIRECT_STATUS\" => \"200\"\n  \"SERVER_NAME\" => \"oceanica.renapp.us\"\n  \"SERVER_PORT\" => \"443\"\n  \"SERVER_ADDR\" => \"**********\"\n  \"REMOTE_PORT\" => \"53354\"\n  \"REMOTE_ADDR\" => \"**********\"\n  \"SERVER_SOFTWARE\" => \"nginx/1.26.3\"\n  \"GATEWAY_INTERFACE\" => \"CGI/1.1\"\n  \"HTTPS\" => \"on\"\n  \"REQUEST_SCHEME\" => \"https\"\n  \"SERVER_PROTOCOL\" => \"HTTP/1.1\"\n  \"DOCUMENT_ROOT\" => \"/var/www/html/public\"\n  \"DOCUMENT_URI\" => \"/index.php\"\n  \"REQUEST_URI\" => \"/servicio/455752/subrogacion\"\n  \"SCRIPT_NAME\" => \"/index.php\"\n  \"CONTENT_LENGTH\" => \"\"\n  \"CONTENT_TYPE\" => \"\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"QUERY_STRING\" => \"\"\n  \"FCGI_ROLE\" => \"RESPONDER\"\n  \"PHP_SELF\" => \"/index.php\"\n  \"REQUEST_TIME_FLOAT\" => 1753278860.9014\n  \"REQUEST_TIME\" => 1753278860\n  \"argv\" => []\n  \"argc\" => 0\n]", "request_cookies": "array:2 [\n  \"XSRF-TOKEN\" => null\n  \"laravel_session_cookie\" => \"BJvB71yW4W7wGKpeFrH8iWn6wGY1p14rZv9U2owd\"\n]", "response_headers": "array:5 [\n  \"cache-control\" => array:1 [\n    0 => \"no-cache, private\"\n  ]\n  \"date\" => array:1 [\n    0 => \"Wed, 23 Jul 2025 13:54:28 GMT\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"text/html; charset=UTF-8\"\n  ]\n  \"set-cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IlBsbUQwaVpBa09HcVlQV1h0cVhLd0E9PSIsInZhbHVlIjoicEF0VU9RSlQ0XC93d0w4MGN6cFJ0R2V4XC9XOTBodTlDNFwvOVRsUnhNemNkeHlaM1l0RWRtQ1FuQTJpamZLSGJOUyIsIm1hYyI6ImMzY2FiYzU4YmQ4ZTA0M2RmZmMzOTkyNDhmNDgxODJjYWNkY2ZkYWZlMjZmYjc3NTNiZWM3ZjMxNTQyZjMzYmMifQ%3D%3D; expires=Thu, 24-Jul-2025 09:54:35 GMT; Max-Age=72000; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6IkNpRkNBdVZNQVV5SXFES1wvd1NSR05BPT0iLCJ2YWx1ZSI6ImsxOVYxNTlHbzBNWWYxaTFoNFpVRWVBZVd3T1gzbEg1YzdETVRoZjZNMmdEMjM4MHhUNnlUd2pETkNcL0FCYXpJU0U3U25xb3FGVGxuRmg1S3VJUWVjd015dUNMNTFBOFRIVkJWZzA5aXpQeEh6dlNneUV4cFwvMXdHSG80dzV4Y3QiLCJtYWMiOiI3YTUzNTlmZTc5Nzk1NmY1N2UyZTU0ZjJlNDk4OGMzNWM4MmZkNGExMzg2ZTM5YjAyNTBmYTIyY2NiNGE5NWM3In0%3D; expires=Thu, 24-Jul-2025 09:54:35 GMT; Max-Age=72000; path=/; httponly\"\n  ]\n  \"Set-Cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IlBsbUQwaVpBa09HcVlQV1h0cVhLd0E9PSIsInZhbHVlIjoicEF0VU9RSlQ0XC93d0w4MGN6cFJ0R2V4XC9XOTBodTlDNFwvOVRsUnhNemNkeHlaM1l0RWRtQ1FuQTJpamZLSGJOUyIsIm1hYyI6ImMzY2FiYzU4YmQ4ZTA0M2RmZmMzOTkyNDhmNDgxODJjYWNkY2ZkYWZlMjZmYjc3NTNiZWM3ZjMxNTQyZjMzYmMifQ%3D%3D; expires=Thu, 24-Jul-2025 09:54:35 GMT; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6IkNpRkNBdVZNQVV5SXFES1wvd1NSR05BPT0iLCJ2YWx1ZSI6ImsxOVYxNTlHbzBNWWYxaTFoNFpVRWVBZVd3T1gzbEg1YzdETVRoZjZNMmdEMjM4MHhUNnlUd2pETkNcL0FCYXpJU0U3U25xb3FGVGxuRmg1S3VJUWVjd015dUNMNTFBOFRIVkJWZzA5aXpQeEh6dlNneUV4cFwvMXdHSG80dzV4Y3QiLCJtYWMiOiI3YTUzNTlmZTc5Nzk1NmY1N2UyZTU0ZjJlNDk4OGMzNWM4MmZkNGExMzg2ZTM5YjAyNTBmYTIyY2NiNGE5NWM3In0%3D; expires=Thu, 24-Jul-2025 09:54:35 GMT; path=/; httponly\"\n  ]\n]", "path_info": "/servicio/455752/subrogacion", "session_attributes": "array:5 [\n  \"_token\" => \"1BVZiIaE3L6XFUCxn5RA5zQOC1lXOznKoNwFmOxc\"\n  \"_previous\" => array:1 [\n    \"url\" => \"https://oceanica-qa.renapp.com/servicio/455752/subrogacion\"\n  ]\n  \"_flash\" => array:2 [\n    \"old\" => []\n    \"new\" => []\n  ]\n  \"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d\" => 18\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n]"}}