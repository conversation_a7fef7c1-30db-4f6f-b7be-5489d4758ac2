<?php $__env->startSection('content_compensation'); ?>
    <div class="ui basic segment">
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Subrogación
            </div>
            <div class="active content">
                <table class="ui celled sortable striped compact very table selectable" id="results">
                    <thead>
                        <tr>
                            <th># identificación</th>
                            <th>Nombre del paciente</th>
                            <th>Nombre del demandado</th>
                            <th>Fecha de accidente</th>
                            <th># caso SORT</th>
                            <th># Expediente</th>
                            <th>Estado del proceso judicial</th>
                            <th>Monto al cobro</th>
                            <th>Ver formulario</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $subrogacion; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                
                                <td class="left aligned">
                                    <?php echo e($row->activity->affiliate->doc_number ?? ''); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e(mb_convert_case($row->activity->affiliate->full_name ?? '', MB_CASE_TITLE, 'UTF-8')); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e(mb_convert_case($row->demandado_nombre ?? '', MB_CASE_TITLE, 'UTF-8')); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e($row->activity->parent_activity->gis_sort->date_accident ? \Carbon\Carbon::parse($row->activity->parent_activity->gis_sort->date_accident)->format('d/m/Y') : ''); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e($row->activity->parent_activity->gis_sort->consecutive_gis ?? ''); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e($row->expediente_judicial); ?>

                                </td>
                                
                                <td class="left aligned">
                                    <?php echo e(ucfirst(strtolower($ESTADOS_PROCESO[$row->estado_proceso] ?? ''))); ?>

                                </td>
                                
                                <td class="right aligned">
                                    <?php echo e(number_format($row->total_costos ?? 0, 2, ',', '.')); ?>

                                </td>
                                
                                <td class="center aligned">
                                    <a href="<?php echo e(url("/servicio/{$row->activity->id}")); ?>" class="ui icon button"
                                        data-tooltip="Ver formulario">
                                        <i class="external eye icon"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>

                
                <br>
                <div class="pagination">
                    <?php if($subrogacion->hasPages()): ?>
                        <div class="ui pagination menu">
                            <?php echo e($subrogacion->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                    <p>Total de registros: <?php echo e($subrogacion->total()); ?></p>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('table.compensation', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>