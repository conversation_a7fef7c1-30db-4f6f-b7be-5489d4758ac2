<div class="title">
    <i class="dropdown icon"></i>
    Costos derivados
</div>
<div class="content">
    @php
        $current = $activityPolicy->policy_sort->type_currency === 'USD' ? '$' : '₡';
    @endphp
    <div class="four fields">

        <div class="field">
            <label for="incapacidades_temporales">Relación de costo de incapacidades temporales
                pagadas</label>
            <div class="ui input">


                <input id="incapacidades_temporales" type="text"
                    value="{{ $current }} {{ number_format($subrogacionSort->incapacidades_temporales ?? 0, 2, ',', '.') ?? '' }}"
                    class="text-right" autocomplete="off" readonly />
            </div>
        </div>

        <div class="field">
            <label for="incapacidades_permanentes">Incapacidades permanentes pagadas</label>
            <div class="ui input">
                <input id="incapacidades_permanentes" type="text" readonly class="text-right" autocomplete="off"
                    value="{{ $subrogacionSort->incapacidades_permanentes ?? '0' }}"
                    placeholder="Ingrese incapacidades permanentes pagadas" />
            </div>
        </div>

        <div class="field">
            <label for="reconocimiento_gastos">Reconocimiento de gastos</label>
            <div class="ui input">

                <input id="reconocimiento_gastos" type="text" readonly
                    value="{{ $current }} {{ number_format($subrogacionSort->reconocimiento_gastos ?? 0, 2, ',', '.') ?? '' }}"
                    class="text-right" autocomplete="off" />
            </div>
        </div>

        <div class="field">
            <label for="prestaciones_medicas">Prestaciones médicas</label>
            <div class="ui input">
                <input id="prestaciones_medicas" type="text" class="text-right"
                    value="{{ $subrogacionSort->prestaciones_medicas ?? '' }}" readonly autocomplete="off"
                    placeholder="Ingrese prestaciones médicas" />
            </div>
        </div>
    </div>

    <div class="four fields">
        <div class="field">
            <label for="total_costos">Otros costos relacionados</label>
            <div class="ui input">

                <input id="total_costos" type="text" autocomplete="off" readonly
                    value="{{ $current }} {{ number_format($subrogacionSort->otros_costos ?? 0, 2, ',', '.') ?? '' }}"
                    class="text-right" class="text-right" placeholder="Ingrese otros costos relacionados" />
            </div>
        </div>
        <div class="field">
            <label for="total_costos">Total de costos</label>
            <div class="ui input">

                <input id="total_costos" type="text" autocomplete="off" readonly
                    value="{{ $current }} {{ number_format($subrogacionSort->total_costos ?? 0, 2, ',', '.') ?? '' }}"
                    class="text-right" class="text-right" placeholder="Ingrese total de costos" />
            </div>
        </div>
    </div>


    <div class="accordion transition">
        <div class="title">
            <i class="dropdown icon"></i>
            Montos del caso
        </div>
        <div class="content">
            <div class="four fields">
                <div class="field">
                    <label for="monto_recuperado">Monto recuperado</label>
                    <div class="ui input">

                        <input id="monto_recuperado" type="text" autocomplete="off" name="monto_recuperado"
                            value="{{$subrogacionSort->monto_recuperado}}" class="text-right" class="text-right"
                            placeholder="Ingrese monto recuperado" />
                    </div>
                </div>
                <div class="field">
                    <label for="monto_no_recuperado">Monto no recuperado</label>
                    <div class="ui input">
                        <input id="monto_no_recuperado" type="text" autocomplete="off" name="monto_no_recuperado"
                            readonly value="{{$subrogacionSort->total_costos - $subrogacionSort->monto_recuperado}}"
                            class="text-right" class="text-right" placeholder="Ingrese monto no recuperado" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    $(document).ready(function () {
        const policy = @json($activityPolicy->policy_sort);
        const subrogacionSort = @json($subrogacionSort);

        const currencyTypes = @json($MONEY_TYPE);
        const symbol = policy.type_currency ? currencyTypes[policy.type_currency]['symbol'] : '';

        configureInputMask('#monto_recuperado', {
            symbol
        });

        configureInputMask('#monto_no_recuperado', {
            symbol
        });


        $('#monto_recuperado').on('input', function () {
            const totalCostos = parseFloat(subrogacionSort.total_costos);
            const montoRecuperado = parseFloat($(this).val().replace(/[$₡]/g, '').replace(/\./g, '').replace(',', '.'));

            if (!isNaN(totalCostos) && !isNaN(montoRecuperado)) {
                const montoNoRecuperado = totalCostos - montoRecuperado;
                $('#monto_no_recuperado').val(montoNoRecuperado);
            } else {
                $('#monto_no_recuperado').val(totalCostos);
            }
        });

    });


</script>