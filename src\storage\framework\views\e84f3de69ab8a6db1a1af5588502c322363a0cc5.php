<div class="title">
    <i class="dropdown icon"></i>
    Estado de cuenta
</div>
<div class="content">
    <table class="ui celled compact selectable table">
        <thead>
            <tr>
                <th>Documento</th>
                <th class="collapsing">Acción</th>
            </tr>
        </thead>

        <tbody>
            <tr>
                <td>Estado de cuenta individual</td>
                <td class="center aligned">
                    <button class="ui icon  button btn-download" data-doc="ESTADO_CUENTA" id="estado_cuenta_individual"
                        data-tooltip="Descargar" style="background: #1CB1D1;color: white;" type="button">
                        <i class="cloud download icon"></i>
                    </button>
                </td>
            </tr>

            <tr>
                <td>Certificación de gastos</td>
                <td class="center aligned">
                    <button class="ui icon  button btn-download" data-doc="CERTIF_GASTOS" id="certificacion_gastos"
                        data-tooltip="Descargar" style="background: #1CB1D1;color: white;" type="button">
                        <i class="cloud download icon"></i>
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
</div>


<script>
    $(function () {

        // Click en cualquier botón con clase .btn-download
        $(document).on('click', '.btn-download', function () {

            const tipo = $(this).data('doc');          // p.e. 'ESTADO_CUENTA'
            if (!tipo) return;

            // Loader modal
            Swal.fire({
                title: 'Generando documento…',
                allowOutsideClick: false,
                didOpen: () => Swal.showLoading()
            });

            $.ajax({
                url: "<?php echo e(url('/servicio/' . $activity->id . '/subrogacion/documento')); ?>", // ruta POST
                method: 'POST',
                data: { tipo },               // body x-www-form-urlencoded
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                xhrFields: { responseType: 'blob' }, // esperamos archivo binario

                success(blob, _status, xhr) {

                    // Nombre sugerido desde header
                    let filename = 'documento.pdf';
                    const disp = xhr.getResponseHeader('Content-Disposition');
                    if (disp && disp.includes('filename=')) {
                        filename = disp.split('filename=')[1].replace(/"/g, '');
                    }

                    // Fuerza descarga
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    window.URL.revokeObjectURL(url);

                    Swal.close();
                },

                error(xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message ?? 'No se pudo descargar el documento.'
                    });
                }
            });
        });
    });
</script>