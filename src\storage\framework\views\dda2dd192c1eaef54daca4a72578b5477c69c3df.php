<div class="title"><i class="dropdown icon"></i>Subrogación</div>

<div class="content">
    <div class="accordion transition">
        <div class="title">
            <i class="dropdown icon"></i>
            Datos relevantes
        </div>
        <div class="content">

            <div class="four fields">
                <!-- Campo Subrogación -->
                <div class="required field">
                    <label for="subrogacion_input">Subrogación</label>
                    <div id="subrogacion_dropdown" class="ui fluid search selection dropdown">
                        <input type="hidden" name="subrogacion_input" id="subrogacion_input"
                            value="<?php echo e($subrogacion ? '1' : '0'); ?>">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona una opción</div>
                        <div class="menu">

                            <div class="item" data-value="1">
                                SI
                            </div>
                            <div class="item" data-value="0">
                                NO
                            </div>

                        </div>
                    </div>
                </div>

                <!-- <PERSON> Soportes -->
                <div class="field">
                    <label for="soportes_input">Soportes</label>
                    <div class="ui fluid file input">
                        <input type="file" name="document3[]" id="soportes_input" accept=".pdf,.doc" multiple />
                    </div>
                </div>
            </div>
        </div>
        <div class="content_subrogacion" id="content_subrogacion" <?php if(!$subrogacion): ?> style="display: none" <?php endif; ?>>
            <div class="title">
                <i class="dropdown icon"></i>
                Costos derivados
            </div>
            <div class="content">
                <?php
                    $current = $activity->parent->policy_sort->type_currency === 'USD' ? '$' : '₡';
                ?>
                <div class="four fields">

                    <div class="field">
                        <label for="incapacidades_temporales">Relación de costo de incapacidades temporales
                            pagadas</label>
                        <div class="ui input">
                            <input type="text" class="text-right" autocomplete="off"
                                placeholder="Ingrese relación de costo de incapacidades"
                                value="<?php echo e($current); ?> <?php echo e($gis_total ? number_format($gis_total->total_amount_pay ?? 0, 2, ',', '.') : '0,00'); ?>"
                                readonly />

                            <input name="incapacidades_temporales" id="incapacidades_temporales" type="hidden"
                                class="text-right" autocomplete="off" value=" <?php echo e($gis_total->total_amount_pay ?? 0); ?>" />
                        </div>
                    </div>

                    <div class="field">
                        <label for="incapacidades_permanentes">Incapacidades permanentes pagadas</label>
                        <div class="ui input">
                            <input name="incapacidades_permanentes" id="incapacidades_permanentes" type="text"
                                class="text-right" value="<?php echo e($gis_total->permanent_count ?? '0'); ?>" autocomplete="off"
                                placeholder="Ingrese incapacidades permanentes pagadas" />
                        </div>
                    </div>

                    <div class="field">
                        <label for="reconocimiento_gastos">Reconocimiento de gastos</label>
                        <div class="ui input">
                            <input type="text" class="text-right"
                                value="<?php echo e($current); ?> <?php echo e(number_format($gis_total->invoices_expense_recognition ?? 0, 2, ',', '.') ?? '0,00'); ?>"
                                readonly autocomplete="off" placeholder="Ingrese reconocimiento de gastos" />
                            <input name="reconocimiento_gastos" id="reconocimiento_gastos" type="hidden"
                                class="text-right" value=" <?php echo e($gis_total->invoices_expense_recognition ?? 0); ?>"
                                autocomplete="off" />
                        </div>
                    </div>

                    <div class="field">
                        <label for="prestaciones_medicas">Prestaciones médicas</label>
                        <div class="ui input">
                            <input name="prestaciones_medicas" id="prestaciones_medicas" type="text" class="text-right"
                                value="<?php echo e($gis_total->medical_count ?? '0'); ?>" autocomplete="off"
                                placeholder="Ingrese prestaciones médicas" />
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <div class="field">
                        <label for="otros_costos">Otros costos relacionados</label>
                        <div class="ui input">
                            <input name="otros_costos" id="otros_costos" type="text" autocomplete="off"
                                class="text-right" class="text-right"
                                placeholder="Ingrese otros costos relacionados " />
                        </div>
                    </div>
                    <div class="field">
                        <label for="total_costos">Total de costos</label>
                        <div class="ui input">
                            <input type="text" autocomplete="off" class="text-right" class="text-right"
                                value="<?php echo e($current); ?> <?php echo e(number_format($gis_total->total_permanent_paid + $gis_total->invoices_expense_recognition + $gis_total->total_amount_pay, 2, ',', '.') ?? '0,00'); ?>"
                                readonly placeholder="Ingrese total de costos" />

                            <input name="total_costos" id="total_costos" type="hidden" autocomplete="off"
                                class="text-right" class="text-right"
                                value=" <?php echo e($gis_total->total_permanent_paid + $gis_total->invoices_expense_recognition + $gis_total->total_amount_pay ?? 0); ?>"
                                placeholder="Ingrese total de costos" />
                        </div>
                    </div>
                </div>

            </div>

            <div class="title">
                <i class="dropdown icon"></i>
                Datos del demandado
            </div>
            <div class="content">

                <div class="four fields">
                    <div class="field required">
                        <label for="demandado_nombre">Nombre del demandado</label>
                        <div class="ui input">
                            <input name="demandado_nombre" id="demandado_nombre" type="text" autocomplete="off"
                                value="<?php echo e($subrogacion->demandado_nombre ?? ''); ?>"
                                placeholder="Ingrese nombre del demandado" />
                        </div>
                    </div>

                    <div class="field required">
                        <label for="demandado_tipo_identificacion">Tipo de identificación</label>
                        <div class="ui fluid search selection dropdown">
                            <input type="hidden" name="demandado_tipo_identificacion"
                                value="<?php echo e($subrogacion->demandado_tipo_identificacion ?? ''); ?>"
                                id="demandado_tipo_identificacion" autocomplete="off">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                <?php $__currentLoopData = $DOC_TYPES; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="item" data-value="<?php echo e($k); ?>"><?php echo e($v); ?>

                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>

                    <div class="field required">
                        <label for="demandado_num_identificacion">Número de identificación</label>
                        <div class="ui input">
                            <input name="demandado_num_identificacion" id="demandado_num_identificacion"
                                value="<?php echo e($subrogacion->demandado_num_identificacion ?? ''); ?>" type="text"
                                autocomplete="off" placeholder="Ingrese número de identificación" />
                        </div>
                    </div>

                    <div class="field required">
                        <label for="demandado_telefono">Teléfono</label>
                        <div class="ui input">
                            <input name="demandado_telefono" id="demandado_telefono" type="tel"
                                value="<?php echo e($subrogacion->demandado_telefono ?? ''); ?>" autocomplete="off"
                                placeholder="Ingrese número de teléfono" />
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <div class="field required">
                        <label for="demandado_email">Correo electrónico</label>
                        <div class="ui input">
                            <input name="demandado_email" id="demandado_email" type="email" autocomplete="off"
                                value="<?php echo e($subrogacion->demandado_email ?? ''); ?>"
                                placeholder="Ingrese correo electrónico" />
                        </div>
                    </div>

                    <div class="field required">
                        <label for="demandado_direccion">Dirección de residencia</label>
                        <div class="ui input">
                            <input name="demandado_direccion" id="demandado_direccion" type="text"
                                value="<?php echo e($subrogacion->demandado_direccion ?? ''); ?>" autocomplete="off"
                                placeholder="Ingrese dirección de residencia" />
                        </div>
                    </div>

                    <div class="field">
                        <label for="rep_legal_nombre">Nombre del representante legal</label>
                        <div class="ui input">
                            <input name="rep_legal_nombre" id="rep_legal_nombre" type="text" autocomplete="off"
                                value="<?php echo e($subrogacion->rep_legal_nombre ?? ''); ?>"
                                placeholder="Ingrese nombre del representante legal" />
                        </div>
                    </div>

                    <div class="field">
                        <label for="rep_legal_cedula">Cédula del representante legal</label>
                        <div class="ui input">
                            <input name="rep_legal_cedula" id="rep_legal_cedula" type="text" autocomplete="off"
                                value="<?php echo e($subrogacion->rep_legal_cedula ?? ''); ?>"
                                placeholder="Ingrese cédula del representante legal" />
                        </div>
                    </div>
                </div>


            </div>

            <div class="title">
                <i class="dropdown icon"></i>
                Caso Judicial por Subrogación
            </div>
            <div class="content">
                <div class="four fields">
                    <div class="field required">
                        <label for="expediente_judicial">Número de expediente judicial asociado</label>
                        <div class="ui input">
                            <input name="expediente_judicial" id="expediente_judicial" type="text"
                                value="<?php echo e($subrogacion->expediente_judicial ?? ''); ?>" autocomplete="off"
                                placeholder="Ingrese número de expediente" />
                        </div>
                    </div>

                    <div class="field required">
                        <label for="juzgado_caso">Juzgado donde se tramita el caso</label>
                        <div class="ui input">
                            <input name="juzgado_caso" id="juzgado_caso" type="text" autocomplete="off"
                                value="<?php echo e($subrogacion->juzgado_caso ?? ''); ?>" placeholder="Ingrese juzgado" />
                        </div>
                    </div>

                    <div class="field required">
                        <label for="numero_proceso">Número de proceso judicial</label>
                        <div class="ui input">
                            <input name="numero_proceso" id="numero_proceso" type="text" autocomplete="off"
                                value="<?php echo e($subrogacion->numero_proceso ?? ''); ?>"
                                placeholder="Ingrese número de proceso" />
                        </div>
                    </div>

                    <div class="field required ">
                        <label for="fecha_inicio_juicio">Fecha de inicio del juicio</label>
                        <div class="ui input">
                            <input name="fecha_inicio_juicio" id="fecha_inicio_juicio" type="date"
                                value="<?php echo e($subrogacion->fecha_inicio_juicio ?? ''); ?>" autocomplete="off" />
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <?php

                        $actualClave = $subrogacion->estado_proceso ?? null;

                        // array de claves en orden
                        $claves = array_keys($ESTADOS_PROCESO);

                        // posición de la clave actual
                        $idxActual = array_search($actualClave, $claves, true);

                        // clave siguiente (o null si no hay)
                        $claveNext = $idxActual !== false && $idxActual + 1 < count($claves)
                            ? $claves[$idxActual + 1]
                            : null;
                    ?>

                    <div class="field required">
                        <label for="estado_proceso">Estado del proceso judicial</label>

                        <div id="estado_proceso_dropdown" class="ui fluid search selection dropdown">
                            <input type="hidden" name="estado_proceso" id="estado_proceso" value="<?php echo e($actualClave); ?>">

                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>

                            <div class="menu">
                                <?php $__currentLoopData = $ESTADOS_PROCESO; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clave => $texto): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $disabled = !($clave === $actualClave || $clave === $claveNext);
                                    ?>
                                    <div class="item <?php echo e($disabled ? 'disabled' : ''); ?>" data-value="<?php echo e($clave); ?>">
                                        <?php echo e($texto); ?>

                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>

<style>
    .text-right {
        text-align: right !important;
    }
</style>

<script>

    function validSubrogracion(subrogacion) {

        rules = {
            ...rules,
            ...subrogacion
        }
        $('#gisForm').form('destroy');


    }

    function deteleValidaionSubrogacion(subrogacion) {
        for (const key in subrogacion) {
            delete rules[key];
        }

        $('#gisForm').form('destroy');
    }

    $(document).ready(function () {

        $('#subrogacion_dropdown').dropdown();
        const addRules = 'subrogacion';

        const subrogacion = validations[addRules]();

        $('#subrogacion_input').on('change', function () {
            const val = $(this).val();
            if (val === '1') {
                $('#content_subrogacion').slideDown(200);

                validSubrogracion(subrogacion);
            } else {
                $('#content_subrogacion').slideUp(200);
                deteleValidaionSubrogacion(subrogacion);
            }
        });
        const policy = <?php echo json_encode($activity->parent->policy_sort, 15, 512) ?>;

        const currencyTypes = <?php echo json_encode($MONEY_TYPE, 15, 512) ?>;
        const symbol = policy.type_currency ? currencyTypes[policy.type_currency]['symbol'] : '';

        configureInputMask('#otros_costos', {
            symbol
        });

    });
</script>