<?php

namespace App;


use Illuminate\Database\Eloquent\Model;

class SubrogacionSort extends Model
{
    protected $table = 'subrogacion_sort';
    protected $fillable = [
        'id',
        'activity_id',
        'incapacidades_temporales',
        'incapacidades_permanentes',
        'reconocimiento_gastos',
        'prestaciones_medicas',
        'total_costos',
        'demandado_nombre',
        'demandado_tipo_identificacion',
        'demandado_num_identificacion',
        'demandado_telefono',
        'demandado_email',
        'demandado_direccion',
        'rep_legal_nombre',
        'rep_legal_cedula',
        'expediente_judicial',
        'juzgado_caso',
        'numero_proceso',
        'fecha_inicio_juicio',
        'estado_proceso',
        'otros_costos',
        'monto_recuperado',

    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

}
