<?php $__env->startSection('title', 'Tablero indemnizaciones'); ?>

<?php $__env->startSection('menu'); ?>
    ##parent-placeholder-252a25667dc7c65fe0e9bf62d474bbab9bab4068##
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="ui basic segment">
        <div class="content ui form">

            <h2 style="color: black;">Tablero Indemnizaciones</h2>

            <div class="ui three item menu">
                <div class="ui dropdown item dropdown_menu">
                    Proveedores
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item <?php echo e($active === 'cuentas_medicas' ? 'active' : ''); ?>"  href="/tablero/indemnizaciones/cuentas_medicas" id="incapacidad_temporal">Cuentas Médicas</a>
                        <a class="item <?php echo e($active === 'cuentas_medicas_proveedores_siniestros' ? 'active' : ''); ?>"  href="/tablero/indemnizaciones/cuentas_medicas_proveedores_siniestros">Cuentas Médicas Proveedores con Siniestro</a>
                        <a class="item <?php echo e($active === 'pagos_admnistrativos' ? 'active' : ''); ?>" href="/tablero/indemnizaciones/pagos_admnistrativos" id="pagos_admnistrativos">Pagos Administrativos</a>
                        <a class="item uploadBankPaymentsBtn" data-origin="proveedores">Carga de archivos de pagos</a>
                    </div>
                </div>
                <a href="/tablero/indemnizaciones/reconocimiento_gastos" class="item <?php echo e($active === 'reconocimiento_gastos' ? 'active' : ''); ?>">Reconocimiento de Gastos</a>
                <div class="ui dropdown item dropdown_menu">
                    Prestaciones Económicas
                    <i class="dropdown icon"></i>
                    <div class="menu">
                       <a class="item <?php echo e($active === 'incapacidad_temporal' ? 'active' : ''); ?>"  href="/tablero/indemnizaciones/incapacidad_temporal" id="incapacidad_temporal">Incapacidades Temporales</a>
                        <a class="item <?php echo e($active === 'incapacidad_permanente' ? 'active' : ''); ?>" href="/tablero/indemnizaciones/incapacidad_permanente" id="incapacidad_permanente">Incapacidades Permanentes</a>
                        <a class="item <?php echo e($active === 'controversias_dictamen' ? 'active' : ''); ?>"  id="controversias_dictamen">Controversias al Dictamen Médico</a>
                        <a class="item <?php echo e($active === 'reaperturas' ? 'active' : ''); ?>" href="/tablero/indemnizaciones/reaperturas" id="reaperturas">Reaperturas</a>
                        <a class="item <?php echo e($active === 'subrogacion' ? 'active' : ''); ?>" href="/tablero/indemnizaciones/subrogacion" id="subrogacion">Subrogación</a>

                        <a class="item <?php echo e($active === 'pagos_fraccinados' ? 'active' : ''); ?>"
                           onclick="handleEmailRequest('pagos_fraccinados')"
                           id="pagos_fraccinados'">Actualizar archivo de pago</a>

                        <a class="item uploadBankPaymentsBtn" data-origin="prestaciones">Carga de archivos de pagos</a>
                    </div>
                </div>
            </div>

            <div style="position: absolute; width: 100%">
                <div style="display: flex; justify-content: center">
                    <img style="height: 70vh; margin: auto"
                         src="<?php echo e(asset('images/mnk.png')); ?>"
                         alt="">
                </div>
            </div>
        </div>
        <?php if(isset($success)): ?>
            <div class="ui blue message" id="success-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    <?php echo e($success); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(\Session::has('success')): ?>
            <div class="ui green message" id="success-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    <?php echo \Session::get('success'); ?>

                </div>
            </div>
        <?php endif; ?>
        <?php if(\Session::has('info')): ?>
            <div class="ui yellow message" id="info-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    <?php echo \Session::get('info'); ?>

                </div>
            </div>
        <?php endif; ?>
        <?php if(\Session::has('error')): ?>
            <?php if(\Session::has('error')): ?>
                <div class="ui negative message">
                    <i class="close icon" id="close-success-message" ></i>
                    <div class="header">
                        <?php echo \Session::get('error'); ?>

                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <?php echo $__env->yieldContent('content_compensation'); ?>

    <style>
        .grayed-input {
            pointer-events: none;
            background-color: #f0f0f0 !important;
            color: #2b2b2b !important;
            border: 1px solid #ddd !important;
            text-transform: none !important;
        }
        .swal2-confirm.btn-black-text {
            background-color: #000 !important;
            color: white !important;
        }

    </style>

    <script>    

        $(document).ready(function () {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();
            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('#close-success-message').on('click', function () {
                $('#success-message').fadeOut();
            });

        });



        function handleEmailRequest(endpoint) {

            loadingMain(true);

            Swal.fire({
                title: '¡Generación de archivos de pagos fraccionados!',
                text: `Al generar nuevamente este archivo, se actualizarán las fracciones a pagar. Estas nuevas fracciones deben ser las utilizadas para el cargue en la plataforma de pagos, ya que las emitidas anteriormente quedarán sin validez debido a posibles nuevas inclusiones en las fracciones:`,
                imageUrl: '/file/client_logo/logo_mnk.png',
                imageHeight: 50,
                imageWidth: 150,
                showCancelButton: true,
                confirmButtonColor: '#91C845',
                cancelButtonColor: '#91C845',
                confirmButtonText: 'Enviar correo',
                cancelButtonText: 'Cancelar',
                //showDenyButton: true,
                //denyButtonText: 'Enviar correo',
                //denyButtonColor: '#91C845',
                allowOutsideClick: true,
                allowEscapeKey: true
            }).then((result) => {

                if (result.isConfirmed) {
                    sendEmail(endpoint);
                //} else if (result.isDenied) {

                } else if (result.dismiss) {
                    loadingMain(false);
                }

            });

            // Agregar estilos para cambiar el color al pasar el mouse
            setTimeout(() => {
                const style = document.createElement('style');
                style.innerHTML = `
                .swal2-confirm:hover,
                .swal2-cancel:hover,
                .swal2-deny:hover {
                    background-color: black !important;
                    border-color: black !important;
                }
            `;
                document.head.appendChild(style);
            }, 100);

            //loadingMain(false);

        }

        function sendEmail(endpoint) {

            loadingMain(true);

            $.ajax({
                url: `/resend_email_fractions`,
                method: 'POST',
                data: JSON.stringify({
                    doc_number: ''
                }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data) {
                    if (data.status === 'success') {
                        loadingMain(false);
                        Swal.fire({
                            imageUrl: '/file/client_logo/logo_mnk.png',
                            imageHeight: 50,
                            imageWidth: 150,
                            title: "Correo enviado exitosamente",
                            text: `Correo enviado exitosamente.\n\n   Si no lo ve en su bandeja de entrada en los próximos minutos, por favor revise su carpeta de spam o correo no deseado, ya que es posible que llegue ahí.\n\n  Gracias por tu atención.`,
                            confirmButtonText: "Aceptar",
                            confirmButtonColor: "#91C845"
                        });
                    } else {
                        loadingMain(false);
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "ERROR: " + data.message,
                        });
                    }
                },
                error: function(xhr, status, error) {
                    loadingMain(false);

                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Ocurrió un error. ' + xhr.responseText,
                    });
                },
                complete: function() {
                    loadingMain(false);
                }
            });

        }


    </script>

    <script>
    $('.uploadBankPaymentsBtn').on('click', function () {
        let origin = $(this).data('origin');

        Swal.fire({
            title: 'Cargar archivo Excel de pagos',
            html: `
                <input type="file" id="excelFile" class="swal2-input" accept=".xls,.xlsx">
                <small style="display:block;margin-top:10px;">Formatos aceptados: .xls, .xlsx</small>
            `,
            showCancelButton: true,
            confirmButtonText: 'Subir',
            confirmButtonColor: "#91C845",
            cancelButtonText: 'Cancelar',
            cancelButtonColor: '#000000',
            preConfirm: () => {
                const fileInput = Swal.getPopup().querySelector('#excelFile');
                if (!fileInput.files || fileInput.files.length === 0) {
                    Swal.showValidationMessage('Debes seleccionar un archivo Excel');
                    return false;
                }
                return fileInput.files[0];
            }
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                const file = result.value;
                const formData = new FormData();
                formData.append('file', file);
                formData.append('_token', '<?php echo e(csrf_token()); ?>');
                formData.append('origin', origin);

                Swal.fire({
                    title: 'Subiendo...',
                    text: 'Por favor espera',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url:  `<?php echo e(secure_url('/upload-bank-payments')); ?>`,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Archivo subido',
                            html: `
                                <p>${response?.message1 || 'El archivo se procesó correctamente.'}</p>
                                <p>${response?.message2 || ''}</p>
                                <p>${response?.message3 || ''}</p>
                                <p>${response?.message4 || ''}</p>
                            `,
                            confirmButtonColor: '#91C845',
                            confirmButtonText: 'Aceptar'
                        });
                    },
                    error: function (xhr) {
                        let errorMsg = 'Ocurrió un error al subir el archivo.';

                        if (xhr.responseJSON) {
                            const response = xhr.responseJSON;

                            // Si hay errores de validación, los unimos en una lista
                            if (response.errors) {
                                const mensajes = [];

                                // Iterar sobre cada campo con errores
                                for (const campo in response.errors) {
                                    if (response.errors.hasOwnProperty(campo)) {
                                        response.errors[campo].forEach(msg => {
                                            mensajes.push(`<p>${msg}</p>`);
                                        });
                                    }
                                }

                                errorMsg = mensajes.join('');
                            } else if (response.message) {
                                // Si solo hay un mensaje general
                                errorMsg = response.message;
                            }
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            html: errorMsg,
                            confirmButtonColor: '#91C845',
                            confirmButtonText: 'Aceptar'
                        });
                    }
                });
            }
        });
    });
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>