<?php $__env->startSection('title', 'subrogación SORT'); ?>

<?php $__env->startSection('menu'); ?>
    ##parent-placeholder-252a25667dc7c65fe0e9bf62d474bbab9bab4068##
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="ui basic segment">

        <!-- Encabezado del formulario -->
        <h1 class="ui header">
            Subrogación
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column">
                    <b># de identificación:</b>
                    <?php echo e($activity->affiliate ? $activity->affiliate->doc_number : ''); ?>

                </div>
                <div class="column"><b>Nombre:</b> <a
                        href="<?php echo e(secure_url('afiliado/' . $activity->affiliate_id)); ?>"><?php echo e(Illuminate\Support\Str::title(mb_strtolower($activity->affiliate->full_name, 'UTF-8'))); ?></a>
                </div>

                <div class="column">
                    <b>Póliza #:</b>
                    <?php echo e($activityPolicy->policy_sort ? $activityPolicy->policy_sort->formatSortNumber() : ''); ?>

                </div>

            </div>
            <div class="ui three columns grid">
                <div class="column">
                    <b>Fecha del caso:</b>
                    <?php echo e($activityGis->gis_sort ? $activityGis->gis_sort->date_accident : ''); ?>

                </div>

                <div class="column">
                    <b># del caso:</b>
                    <?php echo e($activityGis->gis_sort ? $activityGis->gis_sort->formatCaseNumber() : ''); ?>

                </div>

                <div class="column">
                    <b>Proveedor:</b>
                    <?php echo e($activityGis->gis_sort ? $activityGis->gis_sort->provider()->name : ''); ?>

                </div>
            </div>
        </div>

        <form type="submit" class="ui attached form" enctype="multipart/form-data" id="formSubrogacion"
            action="<?php echo e(secure_url('/servicio/' . $activity->id . '/subrogacion/save')); ?>" method="POST" autocomplete="off">

            <?php echo e(csrf_field()); ?>


            <div class="ui styled fluid accordion">

                <?php echo $__env->make('services.subrogacion.form.components.costos_derivados', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <?php echo $__env->make('services.subrogacion.form.components.datos_demandado', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <?php echo $__env->make('services.subrogacion.form.components.caso_judiciales', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <?php echo $__env->make('services.subrogacion.form.components.estado_cuenta', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
            </div>



            <!-- Mostrar errores -->
            <?php if($errors->any()): ?>
                <div class="ui negative message">
                    <div class="header">Errores encontrados</div>
                    <ul class="list">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo $error; ?></li>
                            <!-- Permite que el HTML se interprete correctamente -->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="ui error message hidden"></div>
            <div class="ui basic segment">
                <div class="four fields" style="display: flex;gap: 20px;">
                    <div class="field" style="width: 20%;">
                        <button type="submit" id="submitButton" class="ui primary fluid button"><i class="save icon"></i>
                            Guardar</button>
                    </div>
                    <div class="field">
                        <a href="<?php echo e(secure_url('/servicio/')); ?>" class="ui secondary button"><i class="arrow left icon"></i>
                            Volver
                            a la actividad
                        </a>
                    </div>

                </div>
            </div>
        </form>
    </div>

    <style type="text/css">
        .grayed-input {
            pointer-events: none;

            background-color: #f3f4f5 !important;

            border: 1px solid #ddd !important;

        }

        input[readonly],
        textarea[readonly] {
            background-color: #f3f4f5 !important;
        }
    </style>

    <script>
        $('.ui.accordion').accordion();
        $('.ui.dropdown').dropdown();
    </script>

    <script>
        $(function () {

            const $form = $('#formSubrogacion');
            const $button = $('#submitButton');

            $form.on('submit', function (e) {
                e.preventDefault();                          // evita envío normal

                Swal.fire({
                    title: '¿Guardar subrogación?',
                    text: 'Se enviarán los documentos seleccionados.',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, guardar',
                    cancelButtonText: 'Cancelar'
                }).then(result => {
                    if (!result.isConfirmed) return;           // usuario canceló

                    // 🔄 loader mientras se envía
                    Swal.fire({
                        title: 'Guardando…',
                        allowOutsideClick: false,
                        didOpen: () => Swal.showLoading()
                    });

                    // prepara FormData
                    const formData = new FormData($form[0]);

                    $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: formData,
                        processData: false,          // FormData
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        beforeSend() {
                            $button.addClass('loading disabled');
                        },
                        success(resp) {
                            Swal.fire({
                                icon: 'success',
                                title: '¡Guardado!',
                                text: resp.message || 'Operación realizada correctamente.'
                            }).then(() => {
                                $button.addClass('loading disabled');
                                location.reload();
                            });
                        },
                        error(xhr) {
                            let msg = 'Ocurrió un error.';

                            if (xhr.status === 422 && xhr.responseJSON?.message) {
                                msg = xhr.responseJSON.errors.soportes[0] ?? xhr.responseJSON.message;
                            }
                            Swal.fire({ icon: 'error', title: 'Error', text: msg });
                        },
                        complete() {
                            $button.removeClass('loading disabled');
                        }
                    });
                });
            });
        });
    </script>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.main', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>